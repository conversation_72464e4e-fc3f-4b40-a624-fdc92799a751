-- <PERSON><PERSON>t to update the database schema for enhanced reporting and rating system

-- Create a new table for detailed room ratings
CREATE TABLE IF NOT EXISTS `room_ratings` (
  `rating_id` int(11) NOT NULL AUTO_INCREMENT,
  `review_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `room_type_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `cleanliness_rating` int(11) NOT NULL,
  `comfort_rating` int(11) NOT NULL,
  `staff_rating` int(11) NOT NULL,
  `value_rating` int(11) NOT NULL,
  `amenities_rating` int(11) NOT NULL,
  `overall_rating` int(11) NOT NULL,
  `feedback` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`rating_id`),
  KEY `review_id` (`review_id`),
  KEY `room_id` (`room_id`),
  <PERSON>EY `hotel_id` (`hotel_id`),
  CONSTRAINT `room_ratings_ibfk_1` FOREIGN KEY (`review_id`) REFERENCES `reviews` (`review_id`) ON DELETE CASCADE,
  CONSTRAINT `room_ratings_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE,
  CONSTRAINT `room_ratings_ibfk_3` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create a trigger to send post-checkout notifications
DELIMITER $$
DROP TRIGGER IF EXISTS `booking_completed_trigger` $$
CREATE TRIGGER `booking_completed_trigger` AFTER UPDATE ON `bookings` FOR EACH ROW 
BEGIN
    IF NEW.booking_status = 'completed' AND OLD.booking_status != 'completed' THEN
        INSERT INTO notifications (user_id, title, message, type, related_entity_type, related_entity_id)
        VALUES (NEW.user_id, 'Rate Your Stay', 
               CONCAT('Thank you for staying with us! Please rate your experience at booking #', NEW.booking_id),
               'info', 'booking', NEW.booking_id);
    END IF;
END
$$
DELIMITER ;

-- Create a view for most visited hotels by room type
DROP VIEW IF EXISTS `hotel_visits_by_room_type` $$
CREATE VIEW `hotel_visits_by_room_type` AS
SELECT h.hotel_id, h.hotel_name, rt.room_type_id, rt.type_name, 
       COUNT(*) as visit_count
FROM user_activity_logs l
JOIN rooms r ON l.description LIKE CONCAT('%Room ', r.room_number, '%')
JOIN hotels h ON r.hotel_id = h.hotel_id
JOIN room_types rt ON r.room_type_id = rt.room_type_id
WHERE l.action='page_view' AND l.description LIKE '%rooms page%'
GROUP BY h.hotel_id, rt.room_type_id
ORDER BY visit_count DESC;

-- Create a view for most visited room numbers
DROP VIEW IF EXISTS `most_visited_rooms` $$
CREATE VIEW `most_visited_rooms` AS
SELECT r.room_id, h.hotel_id, h.hotel_name, r.room_number, rt.type_name,
       COUNT(*) as visit_count
FROM user_activity_logs l
JOIN rooms r ON l.description LIKE CONCAT('%Room ', r.room_number, '%')
JOIN hotels h ON r.hotel_id = h.hotel_id
JOIN room_types rt ON r.room_type_id = rt.room_type_id
WHERE l.action='page_view' AND l.description LIKE '%rooms page%'
GROUP BY r.room_id
ORDER BY visit_count DESC;

-- Create a view for hotel sales by date range
DROP VIEW IF EXISTS `hotel_sales_by_date` $$
CREATE VIEW `hotel_sales_by_date` AS
SELECT 
    h.hotel_id,
    h.hotel_name,
    DATE(b.created_at) as booking_date,
    COUNT(b.booking_id) as booking_count,
    SUM(b.total_price) as total_sales
FROM 
    bookings b
JOIN 
    rooms r ON b.room_id = r.room_id
JOIN 
    hotels h ON r.hotel_id = h.hotel_id
WHERE 
    b.payment_status = 'paid'
GROUP BY 
    h.hotel_id, DATE(b.created_at)
ORDER BY 
    booking_date DESC, total_sales DESC;

-- Create a view for room type sales by date range
DROP VIEW IF EXISTS `room_type_sales_by_date` $$
CREATE VIEW `room_type_sales_by_date` AS
SELECT 
    rt.room_type_id,
    rt.type_name,
    h.hotel_id,
    h.hotel_name,
    DATE(b.created_at) as booking_date,
    COUNT(b.booking_id) as booking_count,
    SUM(b.total_price) as total_sales
FROM 
    bookings b
JOIN 
    rooms r ON b.room_id = r.room_id
JOIN 
    hotels h ON r.hotel_id = h.hotel_id
JOIN 
    room_types rt ON r.room_type_id = rt.room_type_id
WHERE 
    b.payment_status = 'paid'
GROUP BY 
    rt.room_type_id, h.hotel_id, DATE(b.created_at)
ORDER BY 
    booking_date DESC, total_sales DESC;

-- Create a view for room ratings summary
DROP VIEW IF EXISTS `room_ratings_summary` $$
CREATE VIEW `room_ratings_summary` AS
SELECT 
    rr.room_id,
    r.room_number,
    rt.type_name,
    h.hotel_id,
    h.hotel_name,
    AVG(rr.cleanliness_rating) as avg_cleanliness,
    AVG(rr.comfort_rating) as avg_comfort,
    AVG(rr.staff_rating) as avg_staff,
    AVG(rr.value_rating) as avg_value,
    AVG(rr.amenities_rating) as avg_amenities,
    AVG(rr.overall_rating) as avg_overall,
    COUNT(rr.rating_id) as rating_count
FROM 
    room_ratings rr
JOIN 
    rooms r ON rr.room_id = r.room_id
JOIN 
    hotels h ON rr.hotel_id = h.hotel_id
JOIN 
    room_types rt ON rr.room_type_id = rt.room_type_id
GROUP BY 
    rr.room_id
ORDER BY 
    avg_overall DESC;
