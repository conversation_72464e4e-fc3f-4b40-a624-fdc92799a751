<?php
// Reset Password Page
include 'includes/header.php';
include 'includes/db.php';

// Check if token and user_id are provided
if (!isset($_GET['token']) || !isset($_GET['user_id']) || empty($_GET['token']) || empty($_GET['user_id'])) {
    $error = "Invalid password reset link.";
} else {
    $token = $_GET['token'];
    $user_id = (int)$_GET['user_id'];
    
    // Check if token exists and is valid
    $stmt = $mysqli->prepare("SELECT * FROM password_resets WHERE user_id = ? AND token = ? AND expires > NOW()");
    $stmt->bind_param('is', $user_id, $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $error = "Invalid or expired password reset link.";
    } else {
        // Token is valid
        $valid = true;
    }
}
?>

<div class="container" style="margin-top:2rem;max-width:500px;">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-key"></i> Reset Password</h4>
        </div>
        <div class="card-body">
            <?php if (isset($_GET['msg']) && $_GET['msg'] == 'error'): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> There was an error resetting your password. Please try again.
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                </div>
                <div class="text-center mt-4">
                    <a href="forgot_password.php" class="btn btn-primary">
                        <i class="fas fa-redo"></i> Request New Reset Link
                    </a>
                </div>
            <?php elseif (isset($valid) && $valid): ?>
                <p class="mb-4">Please enter your new password below.</p>
                
                <form method="post" action="reset_password_process.php" id="resetForm">
                    <input type="hidden" name="token" value="<?= htmlspecialchars($token) ?>">
                    <input type="hidden" name="user_id" value="<?= $user_id ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">New Password</label>
                        <input type="password" class="form-control" name="password" id="password" required minlength="6">
                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" name="confirm_password" id="confirm_password" required>
                        <div id="password-match-feedback" class="invalid-feedback">
                            Passwords do not match.
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> Reset Password
                        </button>
                        <a href="login.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Login
                        </a>
                    </div>
                </form>
                
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const form = document.getElementById('resetForm');
                    const password = document.getElementById('password');
                    const confirmPassword = document.getElementById('confirm_password');
                    const submitBtn = document.getElementById('submitBtn');
                    const feedback = document.getElementById('password-match-feedback');
                    
                    // Check if passwords match
                    function checkPasswordMatch() {
                        if (password.value !== confirmPassword.value) {
                            confirmPassword.classList.add('is-invalid');
                            feedback.style.display = 'block';
                            submitBtn.disabled = true;
                        } else {
                            confirmPassword.classList.remove('is-invalid');
                            feedback.style.display = 'none';
                            submitBtn.disabled = false;
                        }
                    }
                    
                    // Add event listeners
                    password.addEventListener('input', checkPasswordMatch);
                    confirmPassword.addEventListener('input', checkPasswordMatch);
                    
                    // Form submission validation
                    form.addEventListener('submit', function(event) {
                        if (password.value !== confirmPassword.value) {
                            event.preventDefault();
                            confirmPassword.classList.add('is-invalid');
                            feedback.style.display = 'block';
                        }
                    });
                });
                </script>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    padding: 1.25rem;
}

.alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.form-control {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.btn {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
</style>

<?php include 'includes/footer.php'; ?>
