<?php
// User Hotel Search & Listing Page
include 'includes/header.php';
include 'includes/db.php';

$city = $_GET['city'] ?? '';
$type = $_GET['type'] ?? '';
$search = $_GET['search'] ?? '';
$where = [];
if ($city) $where[] = "city LIKE '%".$mysqli->real_escape_string($city)."%'";
if ($type) $where[] = "category_name LIKE '%".$mysqli->real_escape_string($type)."%'";
if ($search) $where[] = "(hotel_name LIKE '%".$mysqli->real_escape_string($search)."%' OR description LIKE '%".$mysqli->real_escape_string($search)."%')";
$whereSql = $where ? 'WHERE '.implode(' AND ', $where) : '';
$res = $mysqli->query("SELECT * FROM hotel_info_view $whereSql ORDER BY star_rating DESC, hotel_name");

// Get all available cities for the dropdown
$cities = $mysqli->query("SELECT DISTINCT city FROM hotels ORDER BY city");
$cityOptions = [];
while($cityRow = $cities->fetch_assoc()) {
    $cityOptions[] = $cityRow['city'];
}

// Get all hotel types for the dropdown
$types = $mysqli->query("SELECT DISTINCT category_name FROM hotel_categories ORDER BY category_name");
$typeOptions = [];
while($typeRow = $types->fetch_assoc()) {
    $typeOptions[] = $typeRow['category_name'];
}
?>

<!-- Hotels Hero Section -->
<style>
.hero-hotels-bg {
    position: relative;
    min-height: 380px;
    background: none;
    overflow: hidden;
}
.hero-hotels-bg::before {
    content: '';
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    background: rgba(15,23,42,0.25); /* lighter overlay for clarity */
    z-index: 1;
}
.hero-hotels-bg img.hero-bg-img {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    object-fit: cover;
    z-index: 0;
    filter: brightness(0.85) contrast(1.08) saturate(1.1); /* clearer, more vibrant */
    transition: filter 0.5s;
}
.hero-hotels-content {
    position: relative;
    z-index: 2;
    padding: 4rem 0 3rem 0;
    text-align: center;
    color: #fff;
}
@media (max-width: 768px) {
    .hero-hotels-bg { min-height: 220px; padding: 1.5rem 0; }
    .hero-hotels-content { padding: 1.5rem 1rem; }
}
/* Animation styles */
.animated-text { opacity: 0; transform: translateY(30px); transition: opacity 0.8s cubic-bezier(.77,0,.18,1), transform 0.8s cubic-bezier(.77,0,.18,1); }
.animated-text.visible { opacity: 1; transform: translateY(0); }
.typewriter {
  display: inline-block;
  border-right: 2px solid #fff;
  white-space: nowrap;
  overflow: hidden;
  animation: blink-caret 0.75s step-end infinite;
}
@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #fff; }
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
.animate-fadein {
  opacity: 0;
  animation: fadeInUp 1s ease forwards;
}
#hero-typewriter {
  min-height: 3.2rem;
  letter-spacing: 1px;
  border-right: 2px solid #fff;
  white-space: nowrap;
  overflow: hidden;
}
.type-cursor {
    display: inline-block;
    width: 1ch;
    color: #fff;
    animation: blink 1s steps(1) infinite;
    font-weight: bold;
    font-size: 1.1em;
}
@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}
</style>
<section class="hero-hotels-bg mb-4">
    <img src="assets/images/hotels/hotel5.jpg" alt="Hotels Hero" class="hero-bg-img">
    <div class="container">
        <div class="hero-hotels-content">
            <h1 class="fw-bold mb-3" id="hero-typewriter" style="font-size:2.5rem; min-height:3.2rem;"></h1>
            <p class="lead mb-4 animate-fadein" id="hero-subheading">Discover the best hotels for your next trip with our advanced search features and exclusive deals.</p>
            <div class="d-flex gap-3 flex-wrap animate-fadein" id="hero-buttons">
                <a href="#search-section" class="btn btn-light btn-lg">Search Now</a>
                <a href="#featured-hotels" class="btn btn-outline-light btn-lg">Featured Hotels</a>
            </div>
        </div>
    </div>
</section>
<script>
// Typewriter effect for hero heading
const phrases = [
  "Find Your Perfect Hotel",
  "Book Your Dream Stay",
  "Discover Top Hotels",
  "Explore Cabadbaran City",
  "Unbeatable Comfort Awaits"
];
let currentPhrase = 0, currentChar = 0, isDeleting = false;
const typeSpeed = 70, eraseSpeed = 35, delayBetween = 1200;
const el = document.getElementById('hero-typewriter');
function typeLoop() {
  if (!el) return;
  const phrase = phrases[currentPhrase];
  if (isDeleting) {
    el.innerHTML = phrase.substring(0, currentChar--) + '<span class="type-cursor">|</span>';
    if (currentChar < 0) {
      isDeleting = false;
      currentPhrase = (currentPhrase + 1) % phrases.length;
      setTimeout(typeLoop, 400);
    } else {
      setTimeout(typeLoop, eraseSpeed);
    }
  } else {
    el.innerHTML = phrase.substring(0, currentChar++) + '<span class="type-cursor">|</span>';
    if (currentChar > phrase.length) {
      setTimeout(() => { isDeleting = true; typeLoop(); }, delayBetween);
    } else {
      setTimeout(typeLoop, typeSpeed);
    }
  }
}
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    typeLoop();
    // Animate subheading and buttons
    document.getElementById('hero-subheading').style.animationDelay = '0.7s';
    document.getElementById('hero-subheading').style.opacity = 1;
    document.getElementById('hero-buttons').style.animationDelay = '1.2s';
    document.getElementById('hero-buttons').style.opacity = 1;
  }, 300);
});
</script>

<!-- Search Section -->
<section id="search-section" class="py-5 bg-light">
    <div class="container">
        <div class="card border-0 shadow">
            <div class="card-body p-4">
                <h4 class="card-title mb-4"><i class="fas fa-search text-primary me-2"></i>Search Hotels</h4>
                <form class="row g-3" method="get">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Hotel Name or Keywords</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-hotel"></i></span>
                            <input type="text" class="form-control" id="search" name="search" placeholder="Search hotels..." value="<?=htmlspecialchars($search)?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="city" class="form-label">Destination</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="city" name="city" placeholder="City" value="<?=htmlspecialchars($city)?>" list="cityList">
                            <datalist id="cityList">
                                <?php foreach($cityOptions as $cityOption): ?>
                                <option value="<?=htmlspecialchars($cityOption)?>">
                                <?php endforeach; ?>
                            </datalist>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">Hotel Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <?php foreach($typeOptions as $typeOption): ?>
                                <option value="<?=htmlspecialchars($typeOption)?>" <?=($type==$typeOption?'selected':'')?>><?=htmlspecialchars($typeOption)?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" type="submit">
                            <i class="fa fa-search me-2"></i> Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Results Section -->
<section id="featured-hotels" class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1">
                    <?php if($search || $city || $type): ?>
                    Search Results
                    <?php else: ?>
                    All Hotels
                    <?php endif; ?>
                </h2>
                <p class="text-muted">
                    <?php if($res->num_rows > 0): ?>
                    Showing <?=$res->num_rows?> hotels
                    <?php if($search): ?>
                    matching "<?=htmlspecialchars($search)?>"
                    <?php endif; ?>
                    <?php if($city): ?>
                    in <?=htmlspecialchars($city)?>
                    <?php endif; ?>
                    <?php if($type): ?>
                    of type <?=htmlspecialchars($type)?>
                    <?php endif; ?>
                    <?php endif; ?>
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="hotels.php" class="btn btn-outline-secondary btn-sm <?=(!$search && !$city && !$type ? 'disabled' : '')?>">
                    <i class="fas fa-times me-1"></i> Clear Filters
                </a>
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-sort me-1"></i> Sort By
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                        <li><a class="dropdown-item" href="#">Rating (High to Low)</a></li>
                        <li><a class="dropdown-item" href="#">Price (Low to High)</a></li>
                        <li><a class="dropdown-item" href="#">Price (High to Low)</a></li>
                        <li><a class="dropdown-item" href="#">Name (A-Z)</a></li>
                    </ul>
                </div>
            </div>
        </div>
    <div class="row g-4">
        <?php
        if($res->num_rows == 0):
        ?>
        <div class="col-12 text-center py-5">
            <div class="empty-state">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No Hotels Found</h4>
                <p class="text-muted">Try adjusting your search criteria or explore our featured hotels.</p>
                <a href="index.php" class="btn btn-primary mt-3">View Featured Hotels</a>
            </div>
        </div>
        <?php
        else:
            while($row = $res->fetch_assoc()):
                // Special-case for J & JM Hotel and Restaurant
                if (trim($row['hotel_name']) === 'J & JM Hotel and Restaurant') {
                    $hotel_image = 'assets/images/hotels/j&jm.jpg';
                } else {
                    $hotel_name_clean = preg_replace('/[^a-zA-Z0-9]+/', '_', $row['hotel_name']);
                    $image_dir = __DIR__ . '/assets/images/hotels/';
                    $image_url_base = 'assets/images/hotels/';
                    $extensions = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
                    $hotel_image = 'assets/images/hotel-placeholder.jpg';
                    $found = false;
                    foreach ($extensions as $ext) {
                        $pattern = $image_dir . $hotel_name_clean . "." . $ext;
                        $files = glob($image_dir . "*.$ext");
                        foreach ($files as $file) {
                            if (strcasecmp(basename($file), $hotel_name_clean . "." . $ext) === 0) {
                                $hotel_image = $image_url_base . basename($file);
                                $found = true;
                                break 2;
                            }
                        }
                    }
                }
        ?>
        <div class="col-md-6 col-lg-4">
            <div class="card hotel-card border-0 h-100">
                <div class="position-relative overflow-hidden">
                    <img src="<?= $hotel_image ?>" class="card-img-top" alt="<?= htmlspecialchars($row['hotel_name']) ?>" style="height: 220px; object-fit: cover; transition: transform 0.5s ease;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-star me-1"></i><?= intval($row['star_rating']) ?>
                        </span>
                    </div>
                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white" style="background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);">
                        <h5 class="mb-0"><?= htmlspecialchars($row['hotel_name']) ?></h5>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-map-marker-alt me-1 small"></i>
                            <span class="small"><?= htmlspecialchars($row['city']) ?></span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge bg-light text-dark me-2">
                            <i class="fas fa-tag me-1 text-success"></i><?= htmlspecialchars($row['category_name']) ?>
                        </span>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-coins me-1 text-primary"></i>
                            <?php if (isset($row['price_range_from'])): ?>
                                From ₱<?= number_format($row['price_range_from'], 2) ?>
                            <?php else: ?>
                                <span class="text-muted">Ask for price</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <p class="card-text text-muted mb-3"><?= htmlspecialchars(substr($row['description'], 0, 100)) ?>...</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="hotel_details.php?id=<?= $row['hotel_id'] ?>" class="btn btn-sm btn-outline-primary">View Details</a>
                        <a href="room.php?hotel_id=<?= $row['hotel_id'] ?>" class="btn btn-sm btn-primary">Browse Rooms</a>
                    </div>
                </div>
            </div>
        </div>
        <?php
            endwhile;
        endif;
        ?>
    </div>

    <!-- Pagination -->
    <?php if($res->num_rows > 0): ?>
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
            </li>
            <li class="page-item active"><a class="page-link" href="#">1</a></li>
            <li class="page-item"><a class="page-link" href="#">2</a></li>
            <li class="page-item"><a class="page-link" href="#">3</a></li>
            <li class="page-item">
                <a class="page-link" href="#">Next</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>
<?php include 'includes/footer.php'; ?>
