<?php
// User Hotel Search & Listing Page
include 'includes/header.php';
include 'includes/db.php';

$city = $_GET['city'] ?? '';
$type = $_GET['type'] ?? '';
$search = $_GET['search'] ?? '';
$where = [];
if ($city) $where[] = "city LIKE '%".$mysqli->real_escape_string($city)."%'";
if ($type) $where[] = "category_name LIKE '%".$mysqli->real_escape_string($type)."%'";
if ($search) $where[] = "(hotel_name LIKE '%".$mysqli->real_escape_string($search)."%' OR description LIKE '%".$mysqli->real_escape_string($search)."%')";
$whereSql = $where ? 'WHERE '.implode(' AND ', $where) : '';
$res = $mysqli->query("SELECT * FROM hotel_info_view $whereSql ORDER BY star_rating DESC, hotel_name");

// Get all available cities for the dropdown
$cities = $mysqli->query("SELECT DISTINCT city FROM hotels ORDER BY city");
$cityOptions = [];
while($cityRow = $cities->fetch_assoc()) {
    $cityOptions[] = $cityRow['city'];
}

// Get all hotel types for the dropdown
$types = $mysqli->query("SELECT DISTINCT category_name FROM hotel_categories ORDER BY category_name");
$typeOptions = [];
while($typeRow = $types->fetch_assoc()) {
    $typeOptions[] = $typeRow['category_name'];
}
?>

<!-- Hotels Hero Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="fw-bold mb-3">Find Your Perfect Hotel</h1>
                <p class="lead mb-4">Discover the best hotels for your next trip with our advanced search features and exclusive deals.</p>
                <div class="d-flex gap-3">
                    <a href="#search-section" class="btn btn-light">Search Now</a>
                    <a href="#featured-hotels" class="btn btn-outline-light">Featured Hotels</a>
                </div>
            </div>
            <div class="col-lg-6 d-none d-lg-block">
                <img src="assets/images/hotel-placeholder.jpg" alt="Hotels" class="img-fluid rounded-3 shadow" style="max-height: 300px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section id="search-section" class="py-5 bg-light">
    <div class="container">
        <div class="card border-0 shadow">
            <div class="card-body p-4">
                <h4 class="card-title mb-4"><i class="fas fa-search text-primary me-2"></i>Search Hotels</h4>
                <form class="row g-3" method="get">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Hotel Name or Keywords</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-hotel"></i></span>
                            <input type="text" class="form-control" id="search" name="search" placeholder="Search hotels..." value="<?=htmlspecialchars($search)?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="city" class="form-label">Destination</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="city" name="city" placeholder="City" value="<?=htmlspecialchars($city)?>" list="cityList">
                            <datalist id="cityList">
                                <?php foreach($cityOptions as $cityOption): ?>
                                <option value="<?=htmlspecialchars($cityOption)?>">
                                <?php endforeach; ?>
                            </datalist>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">Hotel Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <?php foreach($typeOptions as $typeOption): ?>
                                <option value="<?=htmlspecialchars($typeOption)?>" <?=($type==$typeOption?'selected':'')?>><?=htmlspecialchars($typeOption)?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" type="submit">
                            <i class="fa fa-search me-2"></i> Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Results Section -->
<section id="featured-hotels" class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1">
                    <?php if($search || $city || $type): ?>
                    Search Results
                    <?php else: ?>
                    All Hotels
                    <?php endif; ?>
                </h2>
                <p class="text-muted">
                    <?php if($res->num_rows > 0): ?>
                    Showing <?=$res->num_rows?> hotels
                    <?php if($search): ?>
                    matching "<?=htmlspecialchars($search)?>"
                    <?php endif; ?>
                    <?php if($city): ?>
                    in <?=htmlspecialchars($city)?>
                    <?php endif; ?>
                    <?php if($type): ?>
                    of type <?=htmlspecialchars($type)?>
                    <?php endif; ?>
                    <?php endif; ?>
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="hotels.php" class="btn btn-outline-secondary btn-sm <?=(!$search && !$city && !$type ? 'disabled' : '')?>">
                    <i class="fas fa-times me-1"></i> Clear Filters
                </a>
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-sort me-1"></i> Sort By
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                        <li><a class="dropdown-item" href="#">Rating (High to Low)</a></li>
                        <li><a class="dropdown-item" href="#">Price (Low to High)</a></li>
                        <li><a class="dropdown-item" href="#">Price (High to Low)</a></li>
                        <li><a class="dropdown-item" href="#">Name (A-Z)</a></li>
                    </ul>
                </div>
            </div>
        </div>
    <div class="row g-4">
        <?php
        if($res->num_rows == 0):
        ?>
        <div class="col-12 text-center py-5">
            <div class="empty-state">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No Hotels Found</h4>
                <p class="text-muted">Try adjusting your search criteria or explore our featured hotels.</p>
                <a href="index.php" class="btn btn-primary mt-3">View Featured Hotels</a>
            </div>
        </div>
        <?php
        else:
            while($row = $res->fetch_assoc()):
                // Get the correct image path based on hotel name
                $hotel_image = 'assets/images/hotel-placeholder.jpg';
                $hotel_name_clean = strtolower(str_replace(' ', '_', $row['hotel_name']));

                // Check for matching hotel images
                foreach(['casa_alburo', 'eg_hotel', 'gazebo_pools', 'hotel5', 'j_jm_hotel', 'ladolcevita_resort', 'manding_loreta', 'Ruth_Apartelle_Suite'] as $img_name) {
                    if(strpos($img_name, $hotel_name_clean) !== false || strpos($hotel_name_clean, $img_name) !== false) {
                        $hotel_image = "assets/images/hotels/{$img_name}.jpg";
                        break;
                    }
                }
        ?>
        <div class="col-md-6 col-lg-4">
            <div class="card hotel-card border-0 h-100">
                <div class="position-relative overflow-hidden">
                    <img src="<?= $hotel_image ?>" class="card-img-top" alt="<?= htmlspecialchars($row['hotel_name']) ?>" style="height: 220px; object-fit: cover; transition: transform 0.5s ease;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-star me-1"></i><?= intval($row['star_rating']) ?>
                        </span>
                    </div>
                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white" style="background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);">
                        <h5 class="mb-0"><?= htmlspecialchars($row['hotel_name']) ?></h5>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-map-marker-alt me-1 small"></i>
                            <span class="small"><?= htmlspecialchars($row['city']) ?></span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge bg-light text-dark me-2">
                            <i class="fas fa-tag me-1 text-success"></i><?= htmlspecialchars($row['category_name']) ?>
                        </span>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-coins me-1 text-primary"></i>From ₱<?= number_format($row['price_range_from'], 2) ?>
                        </span>
                    </div>
                    <p class="card-text text-muted mb-3"><?= htmlspecialchars(substr($row['description'], 0, 100)) ?>...</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="hotel_details.php?id=<?= $row['hotel_id'] ?>" class="btn btn-sm btn-outline-primary">View Details</a>
                        <a href="room.php?hotel_id=<?= $row['hotel_id'] ?>" class="btn btn-sm btn-primary">Browse Rooms</a>
                    </div>
                </div>
            </div>
        </div>
        <?php
            endwhile;
        endif;
        ?>
    </div>

    <!-- Pagination -->
    <?php if($res->num_rows > 0): ?>
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
            </li>
            <li class="page-item active"><a class="page-link" href="#">1</a></li>
            <li class="page-item"><a class="page-link" href="#">2</a></li>
            <li class="page-item"><a class="page-link" href="#">3</a></li>
            <li class="page-item">
                <a class="page-link" href="#">Next</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>
<?php include 'includes/footer.php'; ?>
