<?php

namespace Psr\Log;

/**
 * Simple implementation of the LoggerInterface.
 */
class SimpleLogger extends AbstractLogger
{
    /**
     * Logs with an arbitrary level.
     *
     * @param mixed  $level
     * @param string $message
     * @param array  $context
     *
     * @return void
     */
    public function log($level, $message, array $context = array())
    {
        // Simple implementation that just echoes the log message
        echo "[{$level}] {$message}" . PHP_EOL;
    }
}
