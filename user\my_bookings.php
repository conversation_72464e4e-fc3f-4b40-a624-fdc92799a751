<?php
// User Bookings Dashboard
include 'includes/session.php';
include 'includes/db.php';

// Require user to be logged in
require_login();

// Get user ID
$user_id = get_user_id();

// Include header after session and redirect checks
include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}
$res = $mysqli->query("SELECT b.*, r.room_number, h.hotel_name FROM bookings b JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.user_id=$user_id ORDER BY b.created_at DESC");
?>
<div class="container" style="margin-top:2rem;max-width:900px;">
    <h2 class="mb-4" style="font-weight:600;">My Bookings</h2>
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Hotel</th>
                    <th>Room</th>
                    <th>Check-in</th>
                    <th>Check-out</th>
                    <th>Status</th>
                    <th>Payment</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            <?php while($row = $res->fetch_assoc()): ?>
                <tr>
                    <td><?=htmlspecialchars($row['hotel_name'])?></td>
                    <td><?=htmlspecialchars($row['room_number'])?></td>
                    <td><?=htmlspecialchars($row['check_in_date'])?></td>
                    <td><?=htmlspecialchars($row['check_out_date'])?></td>
                    <td><span class="badge bg-<?=($row['booking_status']=='confirmed'?'success':($row['booking_status']=='cancelled'?'danger':'warning'))?>"><?=ucfirst($row['booking_status'])?></span></td>
                    <td><span class="badge bg-<?=($row['payment_status']=='paid'?'success':($row['payment_status']=='refunded'?'danger':'secondary'))?>"><?=ucfirst($row['payment_status'])?></span></td>
                    <td>
                        <?php if($row['booking_status']=='pending'): ?>
                        <a href="cancel_booking.php?id=<?=$row['booking_id']?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Cancel this booking?')">Cancel</a>
                        <?php elseif($row['payment_status']=='pending' && $row['booking_status']=='confirmed'): ?>
                        <a href="payment.php?id=<?=$row['booking_id']?>" class="btn btn-sm btn-success">Pay Now</a>
                        <?php else: ?>
                        <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endwhile; ?>
            <?php if ($res->num_rows == 0): ?>
                <tr><td colspan="7" class="text-center text-muted">No bookings found.</td></tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
