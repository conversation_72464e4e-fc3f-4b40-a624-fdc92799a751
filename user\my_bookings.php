<?php
// User Bookings Dashboard
include 'includes/session.php';
include 'includes/db.php';

// Require user to be logged in
require_login();

// Get user ID
$user_id = get_user_id();

// Include header after session and redirect checks
include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();

// Get enhanced booking data with more details
$stmt = $mysqli->prepare("
    SELECT b.*, r.room_number, h.hotel_name, h.city, h.phone as hotel_phone,
           rt.type_name, rt.base_price,
           u.first_name, u.last_name, u.email
    FROM bookings b
    JOIN rooms r ON b.room_id = r.room_id
    JOIN hotels h ON r.hotel_id = h.hotel_id
    JOIN room_types rt ON r.room_type_id = rt.room_type_id
    JOIN users u ON b.user_id = u.user_id
    WHERE b.user_id = ?
    ORDER BY b.created_at DESC
");
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();

// Get booking statistics
$stats_query = $mysqli->prepare("
    SELECT
        COUNT(*) as total_bookings,
        SUM(CASE WHEN booking_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed_bookings,
        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_bookings,
        SUM(CASE WHEN payment_status = 'pending' THEN total_price ELSE 0 END) as pending_amount
    FROM bookings
    WHERE user_id = ?
");
$stats_query->bind_param('i', $user_id);
$stats_query->execute();
$stats = $stats_query->get_result()->fetch_assoc();
?>

<style>
.bookings-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.bookings-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.bookings-title {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #4e73df 0%, #224abe 100%); }
.stat-icon.confirmed { background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%); }
.stat-icon.paid { background: linear-gradient(135deg, #36b9cc 0%, #258391 100%); }
.stat-icon.pending { background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%); }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.bookings-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.booking-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e3e6f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.booking-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
}

.booking-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.booking-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.booking-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.booking-id {
    background: #f8f9fc;
    color: #5a5c69;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.booking-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.booking-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.booking-detail i {
    color: #4e73df;
    width: 16px;
    text-align: center;
}

.booking-detail .label {
    color: #6c757d;
    font-weight: 500;
    min-width: 80px;
}

.booking-detail .value {
    color: #2c3e50;
    font-weight: 600;
}

.booking-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e3e6f0;
}

.booking-badges {
    display: flex;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.confirmed { background: #d1e7dd; color: #0f5132; }
.status-badge.pending { background: #fff3cd; color: #664d03; }
.status-badge.cancelled { background: #f8d7da; color: #842029; }
.status-badge.paid { background: #cff4fc; color: #055160; }
.status-badge.refunded { background: #f8d7da; color: #842029; }

.booking-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.action-btn.pay {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    color: white;
}

.action-btn.pay:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(28, 200, 138, 0.3);
    color: white;
}

.action-btn.cancel {
    background: #f8f9fc;
    color: #e74a3b;
    border: 1px solid #e74a3b;
}

.action-btn.cancel:hover {
    background: #e74a3b;
    color: white;
}

.action-btn.view {
    background: #f8f9fc;
    color: #4e73df;
    border: 1px solid #4e73df;
}

.action-btn.view:hover {
    background: #4e73df;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    color: #e3e6f0;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #5a5c69;
    margin-bottom: 1rem;
}

.empty-state p {
    margin-bottom: 2rem;
}

.empty-state .btn {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
    color: white;
}

.flash-message {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    margin-bottom: 2rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .bookings-container {
        padding: 1rem 0;
    }

    .bookings-title {
        font-size: 2rem;
    }

    .booking-header {
        flex-direction: column;
        gap: 1rem;
    }

    .booking-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .booking-actions {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div class="bookings-container">
    <div class="container">
        <!-- Flash Message -->
        <?php if ($flash): ?>
        <div class="flash-message alert alert-<?= $flash['type'] ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <?= $flash['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="bookings-header">
            <h1 class="bookings-title">
                <i class="fas fa-calendar-check me-3"></i>My Bookings
            </h1>
            <p class="text-center text-muted mb-0">Manage your hotel reservations and payments</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon total">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h3 class="stat-number"><?= $stats['total_bookings'] ?></h3>
                <p class="stat-label">Total Bookings</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon confirmed">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="stat-number"><?= $stats['confirmed_bookings'] ?></h3>
                <p class="stat-label">Confirmed</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon paid">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="stat-number"><?= $stats['paid_bookings'] ?></h3>
                <p class="stat-label">Paid</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="stat-number">₱<?= number_format($stats['pending_amount'], 0) ?></h3>
                <p class="stat-label">Pending Payment</p>
            </div>
        </div>

        <!-- Bookings Content -->
        <div class="bookings-content">
            <?php if ($result->num_rows > 0): ?>
                <?php while($booking = $result->fetch_assoc()):
                    // Calculate nights
                    $checkin = new DateTime($booking['check_in_date']);
                    $checkout = new DateTime($booking['check_out_date']);
                    $nights = $checkin->diff($checkout)->days;
                ?>
                <div class="booking-card">
                    <div class="booking-header">
                        <h4 class="booking-title">
                            <i class="fas fa-hotel me-2"></i><?= htmlspecialchars($booking['hotel_name']) ?>
                        </h4>
                        <span class="booking-id">#<?= $booking['booking_id'] ?></span>
                    </div>

                    <div class="booking-details">
                        <div class="booking-detail">
                            <i class="fas fa-bed"></i>
                            <span class="label">Room:</span>
                            <span class="value"><?= htmlspecialchars($booking['room_number']) ?> (<?= htmlspecialchars($booking['type_name']) ?>)</span>
                        </div>
                        <div class="booking-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="label">Location:</span>
                            <span class="value"><?= htmlspecialchars($booking['city']) ?></span>
                        </div>
                        <div class="booking-detail">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="label">Check-in:</span>
                            <span class="value"><?= date('M j, Y', strtotime($booking['check_in_date'])) ?></span>
                        </div>
                        <div class="booking-detail">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="label">Check-out:</span>
                            <span class="value"><?= date('M j, Y', strtotime($booking['check_out_date'])) ?></span>
                        </div>
                        <div class="booking-detail">
                            <i class="fas fa-moon"></i>
                            <span class="label">Duration:</span>
                            <span class="value"><?= $nights ?> night<?= $nights > 1 ? 's' : '' ?></span>
                        </div>
                        <div class="booking-detail">
                            <i class="fas fa-peso-sign"></i>
                            <span class="label">Total:</span>
                            <span class="value">₱<?= number_format($booking['total_price'], 2) ?></span>
                        </div>
                    </div>

                    <div class="booking-footer">
                        <div class="booking-badges">
                            <span class="status-badge <?= $booking['booking_status'] ?>">
                                <?= ucfirst($booking['booking_status']) ?>
                            </span>
                            <span class="status-badge <?= $booking['payment_status'] ?>">
                                <?= ucfirst($booking['payment_status']) ?>
                            </span>
                        </div>

                        <div class="booking-actions">
                            <?php if($booking['booking_status'] == 'pending'): ?>
                                <a href="cancel_booking.php?id=<?= $booking['booking_id'] ?>"
                                   class="action-btn cancel"
                                   onclick="return confirm('Are you sure you want to cancel this booking?')">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                            <?php elseif($booking['payment_status'] == 'pending' && $booking['booking_status'] == 'confirmed'): ?>
                                <a href="payment.php?id=<?= $booking['booking_id'] ?>" class="action-btn pay">
                                    <i class="fas fa-credit-card me-1"></i>Pay Now
                                </a>
                            <?php elseif($booking['payment_status'] == 'paid'): ?>
                                <a href="receipt.php?id=<?= $booking['booking_id'] ?>" class="action-btn view">
                                    <i class="fas fa-receipt me-1"></i>View Receipt
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-calendar-times"></i>
                    <h4>No Bookings Yet</h4>
                    <p>You haven't made any bookings yet. Start exploring our amazing hotels and make your first reservation!</p>
                    <a href="hotels.php" class="btn">
                        <i class="fas fa-search me-2"></i>Browse Hotels
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
