<?php
// Reset Password Process Handler
include 'includes/db.php';

// Check if all required fields are provided
if (!isset($_POST['token']) || !isset($_POST['user_id']) || !isset($_POST['password']) || !isset($_POST['confirm_password']) ||
    empty($_POST['token']) || empty($_POST['user_id']) || empty($_POST['password']) || empty($_POST['confirm_password'])) {
    header('Location: reset_password.php?msg=error');
    exit;
}

$token = $_POST['token'];
$user_id = (int)$_POST['user_id'];
$password = $_POST['password'];
$confirm_password = $_POST['confirm_password'];

// Validate password
if (strlen($password) < 6) {
    header('Location: reset_password.php?token=' . urlencode($token) . '&user_id=' . urlencode($user_id) . '&msg=error');
    exit;
}

// Check if passwords match
if ($password !== $confirm_password) {
    header('Location: reset_password.php?token=' . urlencode($token) . '&user_id=' . urlencode($user_id) . '&msg=error');
    exit;
}

// Check if token exists and is valid
$stmt = $mysqli->prepare("SELECT * FROM password_resets WHERE user_id = ? AND token = ? AND expires > NOW()");
$stmt->bind_param('is', $user_id, $token);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: reset_password.php?msg=error');
    exit;
}

// Hash the new password
$hashed_password = password_hash($password, PASSWORD_BCRYPT);

// Update the user's password
$stmt = $mysqli->prepare("UPDATE users SET password = ? WHERE user_id = ?");
$stmt->bind_param('si', $hashed_password, $user_id);
$success = $stmt->execute();
$stmt->close();

if (!$success) {
    header('Location: reset_password.php?token=' . urlencode($token) . '&user_id=' . urlencode($user_id) . '&msg=error');
    exit;
}

// Delete the used token
$mysqli->query("DELETE FROM password_resets WHERE user_id = {$user_id}");

// Log the activity
$ip = $_SERVER['REMOTE_ADDR'] ?? '';
$ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
$mysqli->query("INSERT INTO user_activity_logs (user_id, action, description, ip_address, user_agent) 
               VALUES ({$user_id}, 'password_reset', 'Password reset completed', '{$ip}', '{$ua}')");

// Redirect to login page with success message
header('Location: login.php?msg=password_reset');
exit;
