<?php
// User Rooms Listing and Filtering Page
include 'includes/session.php';
include 'includes/db.php';

// Include header after session checks
include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Get parameters from URL
$hotel_id = isset($_GET['hotel_id']) ? intval($_GET['hotel_id']) : 0;
$type = $_GET['type'] ?? '';
$capacity = $_GET['capacity'] ?? '';
$price_min = $_GET['price_min'] ?? '';
$price_max = $_GET['price_max'] ?? '';

// Build query conditions
$where = [];
if ($hotel_id) $where[] = "r.hotel_id = " . $hotel_id;
if ($type) $where[] = "rt.type_name LIKE '%" . $mysqli->real_escape_string($type) . "%'";
if ($capacity) $where[] = "r.capacity >= " . intval($capacity);
if ($price_min) $where[] = "r.price >= " . floatval($price_min);
if ($price_max) $where[] = "r.price <= " . floatval($price_max);

$whereSql = $where ? 'WHERE ' . implode(' AND ', $where) : '';
$sql = "SELECT r.*, h.hotel_name, h.city, rt.type_name
        FROM rooms r
        JOIN hotels h ON r.hotel_id = h.hotel_id
        JOIN room_types rt ON r.room_type_id = rt.room_type_id
        $whereSql
        ORDER BY r.price, h.hotel_name, r.room_number";
$res = $mysqli->query($sql);

// Get all room types for the dropdown
$types = $mysqli->query("SELECT DISTINCT type_name FROM room_types ORDER BY type_name");
$typeOptions = [];
while ($typeRow = $types->fetch_assoc()) {
    $typeOptions[] = $typeRow['type_name'];
}

// Get hotel name if hotel_id is provided
$hotelName = '';
if ($hotel_id) {
    $hotelResult = $mysqli->query("SELECT hotel_name FROM hotels WHERE hotel_id = $hotel_id");
    if ($hotelRow = $hotelResult->fetch_assoc()) {
        $hotelName = $hotelRow['hotel_name'];
    }
}
?>

<!-- Rooms Hero Section -->
<section class="py-5 bg-primary text-white position-relative overflow-hidden">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="fw-bold mb-3 animate__animated animate__fadeInDown" id="room-hero-title">
                    <span class="dynamic-type"></span>
                </h1>
                <p class="lead mb-4 animate__animated animate__fadeInUp animate__delay-1s" id="room-hero-desc">
                    Discover comfortable and stylish accommodations for your stay with our wide selection of room types.
                </p>
            </div>
            <div class="col-lg-6 d-none d-lg-block animate__animated animate__fadeInRight animate__delay-2s">
                <img src="assets/images/rooms/deluxe.jpg" alt="Room" class="img-fluid rounded-3 shadow" style="max-height: 300px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>

<!-- Animated Hero Text Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Typewriter effect for hero title
    const phrases = [
        <?php if ($hotel_id && $hotelName): ?>
        'Rooms at <?= addslashes(htmlspecialchars($hotelName)) ?>',
        'Book Your Stay at <?= addslashes(htmlspecialchars($hotelName)) ?>',
        'Choose Comfort at <?= addslashes(htmlspecialchars($hotelName)) ?>',
        <?php else: ?>
        'Find Your Perfect Room',
        'Book Instantly, Stay Comfortably',
        'Discover Stylish Accommodations',
        'Your Ideal Room Awaits',
        <?php endif; ?>
    ];
    let typeIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let currentPhrase = '';
    const typewriter = document.querySelector('#room-hero-title .dynamic-type');
    function type() {
        const phrase = phrases[typeIndex];
        if (!isDeleting) {
            charIndex++;
            currentPhrase = phrase.substring(0, charIndex);
        } else {
            charIndex--;
            currentPhrase = phrase.substring(0, charIndex);
        }
        typewriter.innerHTML = currentPhrase + '<span class="type-cursor">|</span>';
        if (!isDeleting && charIndex === phrase.length) {
            setTimeout(() => isDeleting = true, 1200);
            setTimeout(type, 1200);
        } else if (isDeleting && charIndex === 0) {
            isDeleting = false;
            typeIndex = (typeIndex + 1) % phrases.length;
            setTimeout(type, 400);
        } else {
            setTimeout(type, isDeleting ? 40 : 70);
        }
    }
    if (typewriter) type();

    // Animate description
    setTimeout(() => {
        document.getElementById('room-hero-desc').classList.add('animate__pulse');
    }, 1000);
});
</script>

<style>
.type-cursor {
    display: inline-block;
    width: 1ch;
    color: #fff;
    animation: blink 1s steps(1) infinite;
    font-weight: bold;
    font-size: 1.1em;
}
@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}
</style>

<!-- Filter Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="card border-0 shadow">
            <div class="card-body p-4">
                <h4 class="card-title mb-4"><i class="fas fa-filter text-primary me-2"></i>Filter Rooms</h4>
                <form class="row g-3" method="get">
                    <?php if ($hotel_id): ?>
                        <input type="hidden" name="hotel_id" value="<?= $hotel_id ?>">
                    <?php endif; ?>

                    <div class="col-md-3">
                        <label for="type" class="form-label">Room Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-bed"></i></span>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <?php foreach ($typeOptions as $typeOption): ?>
                                    <option value="<?= htmlspecialchars($typeOption) ?>" <?= ($type == $typeOption ? 'selected' : '') ?>><?= htmlspecialchars($typeOption) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <label for="capacity" class="form-label">Capacity</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-users"></i></span>
                            <input type="number" class="form-control" id="capacity" name="capacity" placeholder="Min Guests" min="1" value="<?= htmlspecialchars($capacity) ?>">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="price-range" class="form-label">Price Range</label>
                        <div class="d-flex align-items-center">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-coins"></i></span>
                                <input type="number" class="form-control" id="price_min" name="price_min" placeholder="Min" min="0" value="<?= htmlspecialchars($price_min) ?>">
                            </div>
                            <div class="mx-2">-</div>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price_max" name="price_max" placeholder="Max" min="0" value="<?= htmlspecialchars($price_max) ?>">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary flex-grow-1" type="submit">
                                <i class="fa fa-filter me-2"></i> Apply Filters
                            </button>
                            <a href="room.php<?= $hotel_id ? '?hotel_id=' . $hotel_id : '' ?>" class="btn btn-outline-secondary">
                                <i class="fa fa-times me-2"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Results Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1">
                    <?php if ($hotel_id && $hotelName): ?>
                        Available Rooms at <?= htmlspecialchars($hotelName) ?>
                    <?php else: ?>
                        All Available Rooms
                    <?php endif; ?>
                </h2>
                <p class="text-muted">
                    <?php if ($res->num_rows > 0): ?>
                        Showing <?= $res->num_rows ?> rooms
                        <?php if ($type): ?>
                            of type "<?= htmlspecialchars($type) ?>"
                        <?php endif; ?>
                        <?php if ($capacity): ?>
                            with capacity of at least <?= intval($capacity) ?>
                        <?php endif; ?>
                        <?php if ($price_min || $price_max): ?>
                            priced
                            <?= $price_min ? 'from ₱' . number_format($price_min, 2) : '' ?>
                            <?= $price_min && $price_max ? ' to ' : '' ?>
                            <?= $price_max ? '₱' . number_format($price_max, 2) : '' ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </p>
            </div>
            <div class="dropdown">
                <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-sort me-1"></i> Sort By
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                    <li><a class="dropdown-item" href="#">Price (Low to High)</a></li>
                    <li><a class="dropdown-item" href="#">Price (High to Low)</a></li>
                    <li><a class="dropdown-item" href="#">Capacity (High to Low)</a></li>
                    <li><a class="dropdown-item" href="#">Room Type</a></li>
                </ul>
            </div>
        </div>
    <div class="row g-4">
        <?php
        if($res->num_rows == 0):
        ?>
        <div class="col-12 text-center py-5">
            <div class="empty-state">
                <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                <h4>No Rooms Found</h4>
                <p class="text-muted">Try adjusting your filter criteria or check back later for new room listings.</p>
                <a href="room.php" class="btn btn-primary mt-3">View All Rooms</a>
            </div>
        </div>
        <?php
        else:
            while($row = $res->fetch_assoc()):
                // Get the correct image path based on room type
                $room_image = 'assets/images/rooms/room-placeholder.jpg';
                $room_type_clean = strtolower(str_replace(' ', '_', $row['type_name']));

                // Define room type to image mapping
                $roomTypeImages = [
                    'standard' => 'assets/images/rooms/standard.jpg',
                    'deluxe' => 'assets/images/rooms/deluxe.jpg',
                    'suite' => 'assets/images/rooms/suite.jpg',
                    'family' => 'assets/images/rooms/family.jpg',
                    'executive' => 'assets/images/rooms/executive.jpg',
                    'double' => 'assets/images/rooms/double.jpg',
                    'single' => 'assets/images/rooms/standard.jpg',
                    'twin' => 'assets/images/rooms/twin.jpg',
                    'premium' => 'assets/images/rooms/premium.jpg',
                    'presidential' => 'assets/images/rooms/presidential.jpg',
                    'penthouse' => 'assets/images/rooms/penthouse.jpg'
                ];

                // Check for exact matches first
                if (isset($roomTypeImages[$room_type_clean])) {
                    $room_image = $roomTypeImages[$room_type_clean];
                } else {
                    // Check for partial matches
                    foreach($roomTypeImages as $type => $image) {
                        if(strpos($room_type_clean, $type) !== false || strpos($type, $room_type_clean) !== false) {
                            $room_image = $image;
                            break;
                        }
                    }
                }
        ?>
        <div class="col-md-6 col-lg-4">
            <div class="card room-card border-0 h-100">
                <div class="position-relative overflow-hidden">
                    <img src="<?= $room_image ?>" class="card-img-top" alt="<?= htmlspecialchars($row['type_name']) ?> Room" style="height: 220px; object-fit: cover; transition: transform 0.5s ease;">
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-primary">
                            Room <?= htmlspecialchars($row['room_number']) ?>
                        </span>
                    </div>
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-success">
                            <i class="fas fa-coins me-1"></i>₱<?= number_format($row['price'], 2) ?>/night
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0"><?= htmlspecialchars($row['type_name']) ?> Room</h5>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-users me-1"></i><?= intval($row['capacity']) ?> Guests
                        </span>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex align-items-center text-muted mb-2">
                            <i class="fas fa-hotel me-2"></i>
                            <span><?= htmlspecialchars($row['hotel_name']) ?></span>
                        </div>
                        <div class="d-flex align-items-center text-muted">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <span><?= htmlspecialchars($row['city']) ?></span>
                        </div>
                    </div>

                    <p class="card-text text-muted mb-3"><?= htmlspecialchars(substr($row['description'], 0, 100)) ?>...</p>

                    <div class="room-amenities mb-3">
                        <div class="d-flex flex-wrap gap-2">
                            <span class="badge bg-light text-dark"><i class="fas fa-wifi me-1"></i>WiFi</span>
                            <span class="badge bg-light text-dark"><i class="fas fa-tv me-1"></i>TV</span>
                            <span class="badge bg-light text-dark"><i class="fas fa-snowflake me-1"></i>AC</span>
                            <span class="badge bg-light text-dark"><i class="fas fa-bath me-1"></i>Private Bath</span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <a href="booking.php?room_id=<?= $row['room_id'] ?>" class="btn btn-primary">
                            <i class="fas fa-calendar-check me-2"></i>Book Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
            endwhile;
        endif;
        ?>
    </div>

    <!-- Pagination -->
    <?php if($res->num_rows > 9): ?>
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
            </li>
            <li class="page-item active"><a class="page-link" href="#">1</a></li>
            <li class="page-item"><a class="page-link" href="#">2</a></li>
            <li class="page-item"><a class="page-link" href="#">3</a></li>
            <li class="page-item">
                <a class="page-link" href="#">Next</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<!-- Room Booking Modal -->
<div class="modal fade" id="bookingModal" tabindex="-1" aria-labelledby="bookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bookingModalLabel">Book Your Room</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="booking-form" action="process_booking.php" method="post">
                    <input type="hidden" id="modal-room-id" name="room_id">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="check-in-date" class="form-label">Check-in Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="text" class="form-control datepicker" id="check-in-date" name="check_in" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="check-out-date" class="form-label">Check-out Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="text" class="form-control datepicker" id="check-out-date" name="check_out" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="special-requests" class="form-label">Special Requests</label>
                        <textarea class="form-control" id="special-requests" name="special_requests" rows="3" placeholder="Any special requests or preferences?"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Booking Information</h6>
                                <p class="mb-0">Your booking will be confirmed after payment. You can cancel your booking up to 24 hours before check-in for a full refund.</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="booking-form" class="btn btn-primary">Confirm Booking</button>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize booking modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const bookingLinks = document.querySelectorAll('a[href^="booking.php?room_id="]');

    bookingLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Extract room ID from the href
            const roomId = this.href.split('=')[1];

            // Set the room ID in the modal form
            document.getElementById('modal-room-id').value = roomId;

            // Show the modal
            const bookingModal = new bootstrap.Modal(document.getElementById('bookingModal'));
            bookingModal.show();
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
