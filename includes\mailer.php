<?php
/**
 * PHPMailer Implementation
 * 
 * This file provides a centralized way to send emails using PHPMailer
 * throughout the application. It loads email settings from the database
 * and provides a simple interface for sending emails.
 */

// Include PHPMailer classes
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

/**
 * Get email settings from database
 * 
 * @param mysqli $mysqli Database connection
 * @return array Email settings
 */
function getEmailSettings($mysqli) {
    // Default settings
    $settings = [
        'smtp_host' => 'smtp.example.com',
        'smtp_port' => '587',
        'smtp_secure' => 'tls',
        'smtp_auth' => '1',
        'smtp_username' => '<EMAIL>',
        'smtp_password' => '',
        'from_email' => '<EMAIL>',
        'from_name' => 'Hotel Booking System',
    ];
    
    // Load settings from database if they exist
    $result = $mysqli->query("SELECT * FROM system_settings WHERE setting_group = 'email' LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $settings_data = json_decode($row['settings_data'], true);
        if (is_array($settings_data)) {
            $settings = array_merge($settings, $settings_data);
        }
    }
    
    return $settings;
}

/**
 * Send an email using PHPMailer
 * 
 * @param mysqli $mysqli Database connection
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Plain text alternative (optional)
 * @param array $attachments Array of file paths to attach (optional)
 * @param array $cc Array of CC email addresses (optional)
 * @param array $bcc Array of BCC email addresses (optional)
 * @param array $replyTo Reply-to email address and name (optional)
 * @return array Result with success status and message
 */
function sendEmail($mysqli, $to, $subject, $body, $altBody = '', $attachments = [], $cc = [], $bcc = [], $replyTo = null) {
    // Get email settings
    $settings = getEmailSettings($mysqli);
    
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->Port = $settings['smtp_port'];
        
        if ($settings['smtp_secure'] === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($settings['smtp_secure'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = false;
            $mail->SMTPAutoTLS = false;
        }
        
        if ($settings['smtp_auth'] === '1') {
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];
        } else {
            $mail->SMTPAuth = false;
        }
        
        // Recipients
        $mail->setFrom($settings['from_email'], $settings['from_name']);
        $mail->addAddress($to);
        
        // Add CC recipients
        if (!empty($cc)) {
            foreach ($cc as $ccAddress) {
                $mail->addCC($ccAddress);
            }
        }
        
        // Add BCC recipients
        if (!empty($bcc)) {
            foreach ($bcc as $bccAddress) {
                $mail->addBCC($bccAddress);
            }
        }
        
        // Set reply-to address
        if ($replyTo) {
            $mail->addReplyTo($replyTo['email'], $replyTo['name'] ?? '');
        }
        
        // Add attachments
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        
        if (!empty($altBody)) {
            $mail->AltBody = $altBody;
        } else {
            // Create plain text version by stripping HTML
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />', '</p>'], "\n", $body));
        }
        
        // Send the email
        $mail->send();
        
        // Log the email in the database
        logEmail($mysqli, $to, $subject, 'success');
        
        return [
            'success' => true,
            'message' => "Email sent successfully to {$to}!"
        ];
    } catch (Exception $e) {
        // Log the error
        logEmail($mysqli, $to, $subject, 'error', $mail->ErrorInfo);
        
        return [
            'success' => false,
            'message' => "Error sending email: {$mail->ErrorInfo}"
        ];
    }
}

/**
 * Log email sending activity
 * 
 * @param mysqli $mysqli Database connection
 * @param string $recipient Recipient email
 * @param string $subject Email subject
 * @param string $status Success or error
 * @param string $error_message Error message if status is error
 * @return void
 */
function logEmail($mysqli, $recipient, $subject, $status, $error_message = '') {
    // Check if email_logs table exists
    $table_check = $mysqli->query("SHOW TABLES LIKE 'email_logs'");
    
    if ($table_check && $table_check->num_rows == 0) {
        // Create the table if it doesn't exist
        $mysqli->query("CREATE TABLE IF NOT EXISTS email_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            recipient VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
    }
    
    // Insert log entry
    $stmt = $mysqli->prepare("INSERT INTO email_logs (recipient, subject, status, error_message) VALUES (?, ?, ?, ?)");
    $stmt->bind_param('ssss', $recipient, $subject, $status, $error_message);
    $stmt->execute();
    $stmt->close();
}

/**
 * Send a password reset email
 * 
 * @param mysqli $mysqli Database connection
 * @param string $to Recipient email address
 * @param string $token Reset token
 * @param int $user_id User ID
 * @return array Result with success status and message
 */
function sendPasswordResetEmail($mysqli, $to, $token, $user_id) {
    // Get the site URL
    $site_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
    $reset_url = $site_url . "/user/reset_password.php?token=" . urlencode($token) . "&user_id=" . urlencode($user_id);
    
    $subject = "Password Reset Request";
    
    $body = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
        <h2 style="color: #4e73df; text-align: center;">Password Reset Request</h2>
        <p>You have requested to reset your password. Please click the button below to reset your password:</p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="' . $reset_url . '" style="background-color: #4e73df; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">Reset Password</a>
        </div>
        <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
        <p>This link will expire in 24 hours.</p>
        <p>If the button above doesn\'t work, copy and paste the following URL into your browser:</p>
        <p style="word-break: break-all; background-color: #f8f9fc; padding: 10px; border-radius: 4px; font-size: 14px;">' . $reset_url . '</p>
        <p style="text-align: center; margin-top: 30px; color: #858796; font-size: 14px;">
            &copy; ' . date('Y') . ' Hotel Booking System
        </p>
    </div>
    ';
    
    return sendEmail($mysqli, $to, $subject, $body);
}

/**
 * Send a booking confirmation email
 * 
 * @param mysqli $mysqli Database connection
 * @param string $to Recipient email address
 * @param array $booking Booking details
 * @return array Result with success status and message
 */
function sendBookingConfirmationEmail($mysqli, $to, $booking) {
    $subject = "Booking Confirmation - Booking #{$booking['booking_id']}";
    
    // Format dates
    $check_in = date('F j, Y', strtotime($booking['check_in']));
    $check_out = date('F j, Y', strtotime($booking['check_out']));
    
    $body = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
        <h2 style="color: #4e73df; text-align: center;">Booking Confirmation</h2>
        <p>Dear ' . htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']) . ',</p>
        <p>Thank you for your booking! Your reservation has been confirmed.</p>
        
        <div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #4e73df;">Booking Details</h3>
            <p><strong>Booking ID:</strong> ' . htmlspecialchars($booking['booking_id']) . '</p>
            <p><strong>Hotel:</strong> ' . htmlspecialchars($booking['hotel_name']) . '</p>
            <p><strong>Room Type:</strong> ' . htmlspecialchars($booking['room_type']) . '</p>
            <p><strong>Check-in:</strong> ' . $check_in . '</p>
            <p><strong>Check-out:</strong> ' . $check_out . '</p>
            <p><strong>Guests:</strong> ' . htmlspecialchars($booking['adults']) . ' Adults, ' . htmlspecialchars($booking['children']) . ' Children</p>
            <p><strong>Total Amount:</strong> ₱' . number_format($booking['total_amount'], 2) . '</p>
        </div>
        
        <p>If you have any questions or need to make changes to your reservation, please contact us.</p>
        <p>We look forward to welcoming you!</p>
        
        <p style="text-align: center; margin-top: 30px; color: #858796; font-size: 14px;">
            &copy; ' . date('Y') . ' Hotel Booking System
        </p>
    </div>
    ';
    
    return sendEmail($mysqli, $to, $subject, $body);
}
