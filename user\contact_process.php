<?php
// Contact Form Process Handler
include 'includes/db.php';
include '../includes/mailer.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: contact.php');
    exit;
}

// Get form data
$name = trim($_POST['name'] ?? '');
$email = trim($_POST['email'] ?? '');
$subject = trim($_POST['subject'] ?? '');
$message = trim($_POST['message'] ?? '');

// Validate form data
if (empty($name) || empty($email) || empty($subject) || empty($message)) {
    header('Location: contact.php?msg=error');
    exit;
}

// Validate email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    header('Location: contact.php?msg=error');
    exit;
}

// Check if contact_messages table exists
$table_check = $mysqli->query("SHOW TABLES LIKE 'contact_messages'");
if ($table_check->num_rows === 0) {
    // Create contact_messages table if it doesn't exist
    $mysqli->query("CREATE TABLE contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        user_id INT,
        ip_address VARCHAR(45),
        user_agent VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status VARCHAR(20) DEFAULT 'new',
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
    )");
}

// Get user ID if logged in
session_start();
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Get IP address and user agent
$ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// Save message to database
$stmt = $mysqli->prepare("INSERT INTO contact_messages (name, email, subject, message, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
$stmt->bind_param('sssssss', $name, $email, $subject, $message, $user_id, $ip_address, $user_agent);
$result = $stmt->execute();
$stmt->close();

if (!$result) {
    header('Location: contact.php?msg=error');
    exit;
}

// Get admin email from settings
$admin_email = '';
$settings_result = $mysqli->query("SELECT * FROM system_settings WHERE setting_group = 'email' LIMIT 1");
if ($settings_result && $settings_result->num_rows > 0) {
    $row = $settings_result->fetch_assoc();
    $settings_data = json_decode($row['settings_data'], true);
    if (is_array($settings_data) && isset($settings_data['from_email'])) {
        $admin_email = $settings_data['from_email'];
    }
}

// If no admin email found, use a default
if (empty($admin_email)) {
    $admin_email = '<EMAIL>';
}

// Prepare email body
$email_body = '
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
    <h2 style="color: #4e73df; text-align: center;">New Contact Form Submission</h2>
    <p><strong>Name:</strong> ' . htmlspecialchars($name) . '</p>
    <p><strong>Email:</strong> ' . htmlspecialchars($email) . '</p>
    <p><strong>Subject:</strong> ' . htmlspecialchars($subject) . '</p>
    <div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h3 style="margin-top: 0; color: #4e73df;">Message:</h3>
        <p>' . nl2br(htmlspecialchars($message)) . '</p>
    </div>
    <p style="text-align: center; margin-top: 30px; color: #858796; font-size: 14px;">
        Sent from contact form at ' . date('Y-m-d H:i:s') . '<br>
        IP: ' . $ip_address . '
    </p>
</div>
';

// Send email notification to admin
$mail_result = sendEmail(
    $mysqli,
    $admin_email,
    'New Contact Form Submission: ' . $subject,
    $email_body,
    '',
    [],
    [],
    [],
    ['email' => $email, 'name' => $name]
);

// Send confirmation email to user
$confirmation_body = '
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
    <h2 style="color: #4e73df; text-align: center;">Thank You for Contacting Us</h2>
    <p>Dear ' . htmlspecialchars($name) . ',</p>
    <p>Thank you for reaching out to us. We have received your message and will get back to you as soon as possible.</p>
    <div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h3 style="margin-top: 0; color: #4e73df;">Your Message:</h3>
        <p><strong>Subject:</strong> ' . htmlspecialchars($subject) . '</p>
        <p>' . nl2br(htmlspecialchars($message)) . '</p>
    </div>
    <p>If you have any additional questions or information to provide, please feel free to reply to this email.</p>
    <p>Best regards,<br>The Cabadbaran Hotels Team</p>
    <p style="text-align: center; margin-top: 30px; color: #858796; font-size: 14px;">
        &copy; ' . date('Y') . ' Cabadbaran Hotels
    </p>
</div>
';

$confirmation_result = sendEmail(
    $mysqli,
    $email,
    'Thank You for Contacting Us',
    $confirmation_body
);

// Redirect back to contact page with success message
header('Location: contact.php?msg=success');
exit;
