<?php
// get_rooms_by_type.php - Enhanced room data retrieval
include 'includes/db.php';
$type_id = isset($_GET['type_id']) ? intval($_GET['type_id']) : 0;
$rooms = [];
if ($type_id) {
    $res = $mysqli->query("SELECT r.room_id, r.room_number, r.price, r.capacity, r.floor, r.status, h.hotel_name
                          FROM rooms r
                          JOIN hotels h ON r.hotel_id = h.hotel_id
                          WHERE r.room_type_id=$type_id AND r.status='available'
                          ORDER BY CAST(r.room_number AS UNSIGNED) ASC");
    while($row = $res->fetch_assoc()) {
        $rooms[] = $row;
    }
}
header('Content-Type: application/json');
echo json_encode(['rooms' => $rooms]);
