<?php
// get_rooms_by_type.php
include 'includes/db.php';
$type_id = isset($_GET['type_id']) ? intval($_GET['type_id']) : 0;
$rooms = [];
if ($type_id) {
    $res = $mysqli->query("SELECT room_id, room_number, price FROM rooms WHERE room_type_id=$type_id ORDER BY room_number ASC");
    while($row = $res->fetch_assoc()) {
        $rooms[] = $row;
    }
}
header('Content-Type: application/json');
echo json_encode(['rooms' => $rooms]);
