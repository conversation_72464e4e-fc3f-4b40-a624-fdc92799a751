<?php
// User Reviews Page
include 'includes/session.php';
include 'includes/db.php';

// Require user to be logged in
require_login();

// Get user ID
$user_id = get_user_id();

// Include header after session and redirect checks
include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();

// Get enhanced review data with more details
$stmt = $mysqli->prepare("
    SELECT r.*, h.hotel_name, h.city, h.address,
           b.check_in_date, b.check_out_date, b.total_price,
           rm.room_number, rt.type_name,
           u.first_name, u.last_name
    FROM reviews r
    JOIN bookings b ON r.booking_id = b.booking_id
    JOIN rooms rm ON b.room_id = rm.room_id
    JOIN hotels h ON rm.hotel_id = h.hotel_id
    JOIN room_types rt ON rm.room_type_id = rt.room_type_id
    JOIN users u ON b.user_id = u.user_id
    WHERE b.user_id = ?
    ORDER BY r.created_at DESC
");
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();

// Get review statistics
$stats_query = $mysqli->prepare("
    SELECT
        COUNT(*) as total_reviews,
        AVG(rating) as avg_rating,
        COUNT(CASE WHEN rating >= 4 THEN 1 END) as positive_reviews,
        COUNT(CASE WHEN rating <= 2 THEN 1 END) as negative_reviews
    FROM reviews r
    JOIN bookings b ON r.booking_id = b.booking_id
    WHERE b.user_id = ?
");
$stats_query->bind_param('i', $user_id);
$stats_query->execute();
$stats = $stats_query->get_result()->fetch_assoc();

// Get bookings that can be reviewed (paid bookings without reviews)
$reviewable_query = $mysqli->prepare("
    SELECT b.booking_id, h.hotel_name, rm.room_number, rt.type_name,
           b.check_in_date, b.check_out_date
    FROM bookings b
    JOIN rooms rm ON b.room_id = rm.room_id
    JOIN hotels h ON rm.hotel_id = h.hotel_id
    JOIN room_types rt ON rm.room_type_id = rt.room_type_id
    WHERE b.user_id = ?
    AND b.payment_status = 'paid'
    AND b.booking_status = 'confirmed'
    AND b.check_out_date < CURDATE()
    AND NOT EXISTS (SELECT 1 FROM reviews r WHERE r.booking_id = b.booking_id)
    ORDER BY b.check_out_date DESC
    LIMIT 5
");
$reviewable_query->bind_param('i', $user_id);
$reviewable_query->execute();
$reviewable_bookings = $reviewable_query->get_result();
?>

<style>
.reviews-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.reviews-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.reviews-title {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
}

.reviews-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #4e73df 0%, #224abe 100%); }
.stat-icon.average { background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%); }
.stat-icon.positive { background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%); }
.stat-icon.negative { background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%); }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.action-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reviewable-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.reviewable-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e3e6f0;
    transition: all 0.3s ease;
}

.reviewable-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.reviewable-hotel {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.reviewable-details {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.write-review-btn {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.write-review-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(28, 200, 138, 0.3);
    color: white;
}

.reviews-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.review-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e3e6f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.review-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
}

.review-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.review-hotel-info {
    flex-grow: 1;
}

.review-hotel-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.review-hotel-details {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: #ffc107;
    font-size: 1.2rem;
}

.star.empty {
    color: #e3e6f0;
}

.rating-number {
    background: #f8f9fc;
    color: #5a5c69;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.review-comment {
    background: #f8f9fc;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #4e73df;
}

.review-comment-text {
    color: #2c3e50;
    font-style: italic;
    margin: 0;
    line-height: 1.6;
}

.review-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e3e6f0;
}

.review-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.review-booking-info {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    color: #e3e6f0;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #5a5c69;
    margin-bottom: 1rem;
}

.empty-state p {
    margin-bottom: 2rem;
}

.empty-state .btn {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
    color: white;
}

.flash-message {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    margin-bottom: 2rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .reviews-container {
        padding: 1rem 0;
    }

    .reviews-title {
        font-size: 2rem;
    }

    .review-header {
        flex-direction: column;
        gap: 1rem;
    }

    .review-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .reviewable-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="reviews-container">
    <div class="container">
        <!-- Flash Message -->
        <?php if ($flash): ?>
        <div class="flash-message alert alert-<?= $flash['type'] ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <?= $flash['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="reviews-header">
            <h1 class="reviews-title">
                <i class="fas fa-star me-3"></i>My Reviews
            </h1>
            <p class="reviews-subtitle">Share your experiences and help other travelers</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon total">
                    <i class="fas fa-comment-alt"></i>
                </div>
                <h3 class="stat-number"><?= $stats['total_reviews'] ?></h3>
                <p class="stat-label">Total Reviews</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon average">
                    <i class="fas fa-star"></i>
                </div>
                <h3 class="stat-number"><?= $stats['total_reviews'] > 0 ? number_format($stats['avg_rating'], 1) : '0.0' ?></h3>
                <p class="stat-label">Average Rating</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon positive">
                    <i class="fas fa-thumbs-up"></i>
                </div>
                <h3 class="stat-number"><?= $stats['positive_reviews'] ?></h3>
                <p class="stat-label">Positive Reviews</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon negative">
                    <i class="fas fa-thumbs-down"></i>
                </div>
                <h3 class="stat-number"><?= $stats['negative_reviews'] ?></h3>
                <p class="stat-label">Needs Improvement</p>
            </div>
        </div>

        <!-- Reviewable Bookings -->
        <?php if ($reviewable_bookings->num_rows > 0): ?>
        <div class="action-section">
            <h3 class="section-title">
                <i class="fas fa-edit text-primary"></i>
                Write New Reviews
            </h3>
            <p class="text-muted mb-3">You have completed stays that you can review:</p>

            <div class="reviewable-grid">
                <?php while($booking = $reviewable_bookings->fetch_assoc()): ?>
                <div class="reviewable-card">
                    <div class="reviewable-hotel"><?= htmlspecialchars($booking['hotel_name']) ?></div>
                    <div class="reviewable-details">
                        Room <?= htmlspecialchars($booking['room_number']) ?> (<?= htmlspecialchars($booking['type_name']) ?>)<br>
                        Stayed: <?= date('M j, Y', strtotime($booking['check_in_date'])) ?> - <?= date('M j, Y', strtotime($booking['check_out_date'])) ?>
                    </div>
                    <a href="write_review.php?booking_id=<?= $booking['booking_id'] ?>" class="write-review-btn">
                        <i class="fas fa-star"></i>
                        Write Review
                    </a>
                </div>
                <?php endwhile; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Reviews Content -->
        <div class="reviews-content">
            <h3 class="section-title">
                <i class="fas fa-history text-primary"></i>
                Your Review History
            </h3>

            <?php if ($result->num_rows > 0): ?>
                <?php while($review = $result->fetch_assoc()): ?>
                <div class="review-card">
                    <div class="review-header">
                        <div class="review-hotel-info">
                            <h4 class="review-hotel-name">
                                <i class="fas fa-hotel me-2"></i><?= htmlspecialchars($review['hotel_name']) ?>
                            </h4>
                            <p class="review-hotel-details">
                                <i class="fas fa-map-marker-alt me-1"></i><?= htmlspecialchars($review['city']) ?> •
                                <i class="fas fa-bed me-1"></i>Room <?= htmlspecialchars($review['room_number']) ?> (<?= htmlspecialchars($review['type_name']) ?>)
                            </p>
                        </div>

                        <div class="review-rating">
                            <div class="stars">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star star <?= $i <= $review['rating'] ? '' : 'empty' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-number"><?= $review['rating'] ?>/5</span>
                        </div>
                    </div>

                    <?php if (!empty($review['comment'])): ?>
                    <div class="review-comment">
                        <p class="review-comment-text">"<?= htmlspecialchars($review['comment']) ?>"</p>
                    </div>
                    <?php endif; ?>

                    <div class="review-footer">
                        <div class="review-date">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Reviewed on <?= date('F j, Y', strtotime($review['created_at'])) ?>
                        </div>
                        <div class="review-booking-info">
                            <span><i class="fas fa-calendar-check me-1"></i>Stayed: <?= date('M j', strtotime($review['check_in_date'])) ?> - <?= date('M j, Y', strtotime($review['check_out_date'])) ?></span>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-star-half-alt"></i>
                    <h4>No Reviews Yet</h4>
                    <p>You haven't written any reviews yet. Share your experiences to help other travelers!</p>
                    <?php if ($reviewable_bookings->num_rows > 0): ?>
                        <p class="text-muted">Check the "Write New Reviews" section above to review your recent stays.</p>
                    <?php else: ?>
                        <a href="hotels.php" class="btn">
                            <i class="fas fa-search me-2"></i>Browse Hotels
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
