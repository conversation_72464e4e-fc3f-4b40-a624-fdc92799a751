<?php
// User Reviews Page
include 'includes/session.php';
include 'includes/db.php';

// Require user to be logged in
require_login();

// Get user ID
$user_id = get_user_id();

// Include header after session and redirect checks
include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}
$res = $mysqli->query("SELECT r.*, h.hotel_name FROM reviews r JOIN bookings b ON r.booking_id=b.booking_id JOIN hotels h ON b.room_id IN (SELECT room_id FROM rooms WHERE hotel_id=h.hotel_id) WHERE b.user_id=$user_id ORDER BY r.created_at DESC");
?>
<div class="container" style="margin-top:2rem;max-width:800px;">
    <h2 class="mb-4" style="font-weight:600;">My Reviews</h2>
    <a href="write_review.php" class="btn btn-success mb-3">Write a Review</a>
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Hotel</th>
                    <th>Rating</th>
                    <th>Comment</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
            <?php while($row = $res->fetch_assoc()): ?>
                <tr>
                    <td><?=htmlspecialchars($row['hotel_name'])?></td>
                    <td><?=intval($row['rating'])?>/5</td>
                    <td><?=htmlspecialchars($row['comment'])?></td>
                    <td><?=htmlspecialchars($row['created_at'])?></td>
                </tr>
            <?php endwhile; ?>
            <?php if ($res->num_rows == 0): ?>
                <tr><td colspan="4" class="text-center text-muted">No reviews yet.</td></tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
