<?php
// Create system_settings table for PHPMailer config
include '../includes/db.php';
$sql = "CREATE TABLE IF NOT EXISTS system_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  smtp_host VARCHAR(255) NOT NULL,
  smtp_port INT NOT NULL,
  smtp_username VARCHAR(255) NOT NULL,
  smtp_password VARCHAR(255) NOT NULL,
  smtp_secure VARCHAR(10) DEFAULT 'tls',
  from_email VARCHAR(255) NOT NULL,
  from_name VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
if ($mysqli->query($sql)) {
    echo "system_settings table created successfully.";
} else {
    echo "Error creating table: " . $mysqli->error;
}
?>
