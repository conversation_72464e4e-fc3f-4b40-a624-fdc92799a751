<?php
// User Hotel Details Page
include 'includes/header.php';
include 'includes/db.php';
$hotel_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$res = $mysqli->query("SELECT * FROM hotel_info_view WHERE hotel_id=$hotel_id LIMIT 1");
$hotel = $res->fetch_assoc();
if (!$hotel) { echo '<div class="container mt-5"><div class="alert alert-danger">Hotel not found.</div></div>'; include 'includes/footer.php'; exit; }
// Fetch room types for this hotel
$room_types = $mysqli->query("SELECT rt.*, COUNT(r.room_id) as available_rooms FROM room_types rt JOIN rooms r ON rt.room_type_id=r.room_type_id WHERE r.hotel_id=$hotel_id AND r.status='available' GROUP BY rt.room_type_id");
?>
<div class="container" style="margin-top:2rem;">
    <div class="row mb-4">
        <div class="col-md-6">
            <?php
            // Improved: Always match hotel image by hotel_id for accuracy
            $hotel_images = [
                1 => 'gazebo_pools_and_restaurant.jpg',
                2 => 'casa_alburo_hotel_and_restaurant.jpg',
                3 => 'e_&_g_hotel_and_restaurant.jpg',
                4 => 'j&jm.jpg',
                5 => 'manding_loreta_resort.jpg',
                6 => 'ladolcevita_inland_resort.jpg',
                7 => 'ruth_apartelle_suite.jpg',
            ];
            $hotel_image = isset($hotel_images[$hotel_id]) ? 'assets/images/hotels/' . $hotel_images[$hotel_id] : 'assets/images/hotel-placeholder.jpg';
            if (!file_exists($hotel_image)) $hotel_image = 'assets/images/hotel-placeholder.jpg';
            ?>
            <img src="<?= htmlspecialchars($hotel_image) ?>" class="img-fluid rounded shadow" alt="<?= htmlspecialchars($hotel['hotel_name']) ?> Image" style="max-height:340px;object-fit:cover;">
        </div>
        <div class="col-md-6">
            <h2 style="font-weight:700;"><?=htmlspecialchars($hotel['hotel_name'])?></h2>
            <div class="mb-2"><i class="fa fa-map-marker-alt"></i> <?=htmlspecialchars($hotel['address'])?>, <?=htmlspecialchars($hotel['city'])?></div>
            <div class="mb-2"><i class="fa fa-star text-warning"></i> <?=intval($hotel['star_rating'])?> Star</div>
            <div class="mb-2"><i class="fa fa-leaf text-success"></i> <?=htmlspecialchars($hotel['category_name'])?></div>
            <p><?=htmlspecialchars($hotel['description'])?></p>
        </div>
    </div>
    <h3 class="mb-3" style="font-weight:600;">Room Types</h3>
    <div class="row">
        <?php while($type = $room_types->fetch_assoc()): ?>
        <div class="col-md-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-body">
                    <h5 class="card-title"><?=htmlspecialchars($type['type_name'])?></h5>
                    <div class="mb-2"><i class="fa fa-users"></i> Capacity: <?=intval($type['capacity'])?></div>
                    <div class="mb-2"><i class="fa fa-money-bill"></i> ₱<?=number_format($type['base_price'],2)?></div>
                    <div class="mb-2"><i class="fa fa-bed"></i> Available: <?=intval($type['available_rooms'])?></div>
                    <div class="mb-2"><i class="fa fa-info-circle"></i> <?=htmlspecialchars($type['description'])?></div>
                    <a href="choose_room.php?hotel_id=<?=$hotel_id?>&type_id=<?=$type['room_type_id']?>" class="btn btn-outline-success w-100">Choose Room</a>
                </div>
            </div>
        </div>
        <?php endwhile; ?>
        <?php if ($room_types->num_rows == 0): ?>
        <div class="col-12 text-center text-muted">No available room types at this hotel.</div>
        <?php endif; ?>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
