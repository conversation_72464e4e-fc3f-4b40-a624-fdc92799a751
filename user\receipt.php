<?php
// Booking Receipt Page
include 'includes/db.php';
include 'includes/header.php';
$booking_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if (!$booking_id) { echo '<div class="container mt-5"><div class="alert alert-danger">Invalid booking.</div></div>'; include 'includes/footer.php'; exit; }
// Fetch booking, user, room, hotel
$res = $mysqli->query("SELECT b.*, u.first_name, u.last_name, u.email, r.room_number, r.price as room_price, h.hotel_name FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.booking_id=$booking_id LIMIT 1");
$booking = $res ? $res->fetch_assoc() : null;
if (!$booking) { echo '<div class="container mt-5"><div class="alert alert-danger">Booking not found.</div></div>'; include 'includes/footer.php'; exit; }
// Fetch amenities (built-in and extra)
$am_res = $mysqli->query("SELECT a.amenity_name as name, ba.custom_name, 0 as price, ba.custom_price FROM booking_amenities ba LEFT JOIN amenities a ON ba.amenity_id=a.amenity_id WHERE ba.booking_id=$booking_id");
$amenities = [];
$total_amenities = 0;
while ($am = $am_res->fetch_assoc()) {
    $name = $am['custom_name'] ?: $am['name'];
    $price = $am['custom_price'] !== null ? $am['custom_price'] : $am['price'];
    if ($name && $price !== null) {
        $amenities[] = ['name'=>$name, 'price'=>$price];
        $total_amenities += floatval($price);
    }
}
$total = floatval($booking['room_price']) + $total_amenities;
?>
<div class="container" style="margin-top:2rem;max-width:700px;">
    <?php if (isset($_GET['success']) && $_GET['success'] == '1'): ?>
        <div class="alert alert-success text-center fw-bold fs-4 mb-4 animate__animated animate__fadeInDown">
            <i class="fas fa-check-circle me-2"></i>Successfully Booked! Thank you for your reservation.
        </div>
    <?php elseif (isset($_GET['success']) && $_GET['success'] == '0'): ?>
        <div class="alert alert-warning text-center fw-bold fs-5 mb-4 animate__animated animate__fadeInDown">
            <i class="fas fa-exclamation-circle me-2"></i>Booking successful, but email receipt could not be sent.
        </div>
    <?php endif; ?>
    <div class="card shadow-lg border-0 rounded-4 animate__animated animate__fadeInDown">
        <div class="card-body p-5">
            <h2 class="mb-4 fw-bold text-primary"><i class="fas fa-receipt me-2"></i>Booking Receipt</h2>
            <div class="mb-3"><strong>Booking ID:</strong> #<?= $booking_id ?></div>
            <div class="mb-3"><strong>Name:</strong> <?= htmlspecialchars($booking['first_name'].' '.$booking['last_name']) ?></div>
            <div class="mb-3"><strong>Email:</strong> <?= htmlspecialchars($booking['email']) ?></div>
            <div class="mb-3"><strong>Hotel:</strong> <?= htmlspecialchars($booking['hotel_name']) ?></div>
            <div class="mb-3"><strong>Room Number:</strong> <?= htmlspecialchars($booking['room_number']) ?></div>
            <div class="mb-3"><strong>Check-in:</strong> <?= htmlspecialchars($booking['check_in_date']) ?> <strong>Check-out:</strong> <?= htmlspecialchars($booking['check_out_date']) ?></div>
            <div class="mb-3"><strong>Status:</strong> <span class="badge bg-<?= $booking['booking_status']=='confirmed'?'success':($booking['booking_status']=='cancelled'?'danger':'warning') ?>"><?= ucfirst($booking['booking_status']) ?></span></div>
            <hr>
            <h5 class="mb-3">Charges</h5>
            <ul class="list-group mb-3">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Room Price <span>₱<?= number_format($booking['room_price'],2) ?></span>
                </li>
                <?php foreach($amenities as $am): ?>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <?= htmlspecialchars($am['name']) ?> <span>₱<?= number_format($am['price'],2) ?></span>
                </li>
                <?php endforeach; ?>
                <li class="list-group-item d-flex justify-content-between align-items-center fw-bold">
                    Total <span>₱<?= number_format($total,2) ?></span>
                </li>
            </ul>
            <div class="alert alert-info text-center mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <b>Thank you for booking with SmartHotel!</b><br>
                Please check your email for your official receipt and further instructions. We look forward to welcoming you!
            </div>
            <div class="text-center mt-4">
                <a href="my_bookings.php" class="btn btn-outline-primary">Back to My Bookings</a>
                <button class="btn btn-primary ms-2" onclick="window.print()"><i class="fa fa-print me-1"></i>Print Receipt</button>
            </div>
        </div>
    </div>
</div>
<style>.card.animate__fadeInDown {animation-duration: 0.7s;}</style>
<?php include 'includes/footer.php'; ?>
