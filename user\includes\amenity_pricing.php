<?php
/**
 * Centralized Amenity Pricing Functions
 * Since the amenities table doesn't have a price column,
 * we manage pricing through this centralized system
 */

/**
 * Get the price for a specific amenity
 * @param int $amenity_id The amenity ID
 * @return float The price for the amenity
 */
function getAmenityPrice($amenity_id) {
    $amenity_prices = [
        1 => 500,  // Spa Access
        2 => 300,  // Lunch Meal
        3 => 450,  // Dinner Meal
        4 => 150,  // Snacks Package
        5 => 800,  // In-Room Massage
        6 => 200   // Default for any other amenity
    ];
    
    return $amenity_prices[$amenity_id] ?? 200;
}

/**
 * Get all amenity prices
 * @return array Array of amenity_id => price
 */
function getAllAmenityPrices() {
    return [
        1 => 500,  // Spa Access
        2 => 300,  // Lunch Meal
        3 => 450,  // Dinner Meal
        4 => 150,  // Snacks Package
        5 => 800,  // In-Room Massage
        6 => 200   // Default for any other amenity
    ];
}

/**
 * Calculate total price for multiple amenities
 * @param array $amenity_ids Array of amenity IDs
 * @return float Total price for all amenities
 */
function calculateAmenitiesTotal($amenity_ids) {
    $total = 0;
    $prices = getAllAmenityPrices();
    
    foreach ($amenity_ids as $amenity_id) {
        $total += $prices[$amenity_id] ?? 200;
    }
    
    return $total;
}

/**
 * Get amenity details with pricing
 * @param mysqli $mysqli Database connection
 * @param int $amenity_id Amenity ID
 * @return array|null Amenity details with price
 */
function getAmenityWithPrice($mysqli, $amenity_id) {
    $query = "SELECT amenity_id, amenity_name, description, icon FROM amenities WHERE amenity_id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("i", $amenity_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($amenity = $result->fetch_assoc()) {
        $amenity['price'] = getAmenityPrice($amenity_id);
        return $amenity;
    }
    
    return null;
}

/**
 * Get all amenities with pricing
 * @param mysqli $mysqli Database connection
 * @return array Array of amenities with prices
 */
function getAllAmenitiesWithPrices($mysqli) {
    $query = "SELECT amenity_id, amenity_name, description, icon FROM amenities ORDER BY amenity_name";
    $result = $mysqli->query($query);
    $amenities = [];
    $prices = getAllAmenityPrices();
    
    while ($amenity = $result->fetch_assoc()) {
        $amenity['price'] = $prices[$amenity['amenity_id']] ?? 200;
        $amenities[] = $amenity;
    }
    
    return $amenities;
}

/**
 * Format price for display
 * @param float $price The price to format
 * @return string Formatted price string
 */
function formatPrice($price) {
    return '₱' . number_format($price, 2);
}

/**
 * Get amenity icon mapping
 * @return array Array of amenity_id => icon_class
 */
function getAmenityIcons() {
    return [
        1 => 'spa',        // Spa Access
        2 => 'utensils',   // Lunch Meal
        3 => 'utensils',   // Dinner Meal
        4 => 'coffee',     // Snacks Package
        5 => 'spa',        // In-Room Massage
        6 => 'star'        // Default
    ];
}

/**
 * Get icon for specific amenity
 * @param int $amenity_id Amenity ID
 * @return string FontAwesome icon class
 */
function getAmenityIcon($amenity_id) {
    $icons = getAmenityIcons();
    return $icons[$amenity_id] ?? 'star';
}
?>
