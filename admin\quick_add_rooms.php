<?php
// Quick Room Generator for Admin
include 'includes/header.php';
include 'includes/db.php';

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $hotel_id = (int)$_POST['hotel_id'];
    $room_type_id = (int)$_POST['room_type_id'];
    $start_number = (int)$_POST['start_number'];
    $count = (int)$_POST['count'];
    $base_price = (float)$_POST['base_price'];
    
    $created = 0;
    $errors = [];
    
    for ($i = 0; $i < $count; $i++) {
        $room_number = str_pad($start_number + $i, 2, '0', STR_PAD_LEFT);
        $floor = ceil(($start_number + $i) / 10);
        $price = $base_price + rand(-200, 500); // Add some price variation
        
        // Check if room already exists
        $check = $mysqli->prepare("SELECT room_id FROM rooms WHERE hotel_id = ? AND room_number = ?");
        $check->bind_param("is", $hotel_id, $room_number);
        $check->execute();
        
        if ($check->get_result()->num_rows == 0) {
            // Get room type info for capacity
            $type_query = $mysqli->prepare("SELECT capacity FROM room_types WHERE room_type_id = ?");
            $type_query->bind_param("i", $room_type_id);
            $type_query->execute();
            $type_result = $type_query->get_result();
            $capacity = $type_result->fetch_assoc()['capacity'] ?? 2;
            
            // Insert room
            $insert = $mysqli->prepare("INSERT INTO rooms (hotel_id, room_type_id, room_number, floor, price, capacity, status) VALUES (?, ?, ?, ?, ?, ?, 'available')");
            $insert->bind_param("iisidi", $hotel_id, $room_type_id, $room_number, $floor, $price, $capacity);
            
            if ($insert->execute()) {
                $created++;
            } else {
                $errors[] = "Failed to create room $room_number";
            }
        } else {
            $errors[] = "Room $room_number already exists";
        }
    }
    
    $message = "Created $created rooms successfully.";
    if (!empty($errors)) {
        $message .= " Errors: " . implode(', ', array_slice($errors, 0, 5));
        if (count($errors) > 5) {
            $message .= " and " . (count($errors) - 5) . " more...";
        }
    }
}

// Get hotels and room types for dropdowns
$hotels = $mysqli->query("SELECT hotel_id, hotel_name FROM hotels ORDER BY hotel_name");
$room_types = $mysqli->query("SELECT room_type_id, type_name, base_price FROM room_types ORDER BY type_name");
?>

<div class="admin-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Quick Room Generator</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-info"><?= htmlspecialchars($message) ?></div>
                        <?php endif; ?>
                        
                        <form method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Hotel</label>
                                        <select name="hotel_id" class="form-select" required>
                                            <option value="">Select Hotel</option>
                                            <?php while($hotel = $hotels->fetch_assoc()): ?>
                                                <option value="<?= $hotel['hotel_id'] ?>"><?= htmlspecialchars($hotel['hotel_name']) ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Room Type</label>
                                        <select name="room_type_id" class="form-select" required id="roomTypeSelect">
                                            <option value="">Select Room Type</option>
                                            <?php while($type = $room_types->fetch_assoc()): ?>
                                                <option value="<?= $type['room_type_id'] ?>" data-price="<?= $type['base_price'] ?>"><?= htmlspecialchars($type['type_name']) ?> (₱<?= number_format($type['base_price'], 2) ?>)</option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Starting Room Number</label>
                                        <input type="number" name="start_number" class="form-control" value="1" min="1" max="999" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Number of Rooms</label>
                                        <input type="number" name="count" class="form-control" value="5" min="1" max="50" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Base Price</label>
                                        <input type="number" name="base_price" class="form-control" id="basePriceInput" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> Rooms will be created with slight price variations (±₱500) for realism. Floor numbers will be calculated automatically.
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Generate Rooms
                            </button>
                            <a href="rooms.php" class="btn btn-secondary">
                                <i class="fas fa-list me-2"></i>View All Rooms
                            </a>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Room Statistics</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get room statistics
                        $stats = $mysqli->query("SELECT 
                            h.hotel_name,
                            rt.type_name,
                            COUNT(*) as room_count,
                            MIN(r.price) as min_price,
                            MAX(r.price) as max_price
                            FROM rooms r 
                            JOIN hotels h ON r.hotel_id = h.hotel_id 
                            JOIN room_types rt ON r.room_type_id = rt.room_type_id 
                            GROUP BY h.hotel_id, rt.room_type_id 
                            ORDER BY h.hotel_name, rt.type_name");
                        ?>
                        
                        <?php if ($stats->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Hotel</th>
                                            <th>Type</th>
                                            <th>Count</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($stat = $stats->fetch_assoc()): ?>
                                            <tr>
                                                <td class="small"><?= htmlspecialchars(substr($stat['hotel_name'], 0, 15)) ?></td>
                                                <td class="small"><?= htmlspecialchars($stat['type_name']) ?></td>
                                                <td><span class="badge bg-primary"><?= $stat['room_count'] ?></span></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No rooms created yet.</p>
                        <?php endif; ?>
                        
                        <?php
                        // Total room count
                        $total_rooms = $mysqli->query("SELECT COUNT(*) as total FROM rooms")->fetch_assoc()['total'];
                        ?>
                        <div class="mt-3 p-3 bg-light rounded">
                            <strong>Total Rooms: <?= $total_rooms ?></strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-fill base price when room type is selected
document.getElementById('roomTypeSelect').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const basePrice = selectedOption.getAttribute('data-price');
    if (basePrice) {
        document.getElementById('basePriceInput').value = basePrice;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
