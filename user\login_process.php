<?php
// User Login Handler
include 'includes/session.php'; // Include session management
include 'includes/db.php';

// Get form data
$email = $_POST['email'] ?? '';
$password = $_POST['password'] ?? '';

// Validate form data
if (!$email || !$password) {
    set_flash_message('Please fill all fields', 'danger');
    header('Location: login.php');
    exit;
}
$res = $mysqli->query("SELECT user_id, password FROM users WHERE email='$email'");
if ($row = $res->fetch_assoc()) {
    if (password_verify($password, $row['password'])) {
        // Set user session
        $_SESSION['user_id'] = $row['user_id'];

        // Log user activity
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $ua = $_SERVER['HTTP_USER_AGENT'] ?? '';

        try {
            $mysqli->query("INSERT INTO user_activity_logs (user_id, action, description, ip_address, user_agent)
                           VALUES ({$row['user_id']}, 'login', 'User logged in', '$ip', '$ua')");
        } catch (Exception $e) {
            // If logging fails, continue anyway - this is not critical
        }

        // Set success message and redirect
        set_flash_message('Login successful', 'success');
        header('Location: index.php');
        exit;
    }
}

// If we get here, login failed
set_flash_message('Invalid email or password', 'danger');
header('Location: login.php');
exit;
