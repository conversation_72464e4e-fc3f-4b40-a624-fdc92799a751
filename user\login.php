<?php
// User Login Page
include 'includes/session.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: index.php');
    exit;
}

include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Display URL message if any (for backward compatibility)
if (isset($_GET['msg'])) {
    $msg = htmlspecialchars($_GET['msg']);
    echo "<div class='container mt-3'><div class='alert alert-info alert-dismissible fade show' role='alert'>
            {$msg}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}
?>
<div class="container" style="margin-top:2rem;max-width:400px;">
    <h2 class="mb-4 text-center" style="font-weight:600;">User Login</h2>
    <form method="post" action="login_process.php" autocomplete="off">
        <div class="mb-3">
            <label class="form-label">Email</label>
            <input type="email" class="form-control" name="email" required autofocus>
        </div>
        <div class="mb-3">
            <label class="form-label">Password</label>
            <input type="password" class="form-control" name="password" required>
        </div>
        <button class="btn btn-primary w-100" type="submit">Login</button>
        <div class="text-center mt-3">
            Don't have an account? <a href="register.php">Register</a>
        </div>
    </form>
</div>
<?php include 'includes/footer.php'; ?>
