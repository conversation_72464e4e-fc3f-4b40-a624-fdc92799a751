<?php
// User Login Page
include 'includes/session.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: index.php');
    exit;
}

include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Display URL message if any (for backward compatibility)
if (isset($_GET['msg'])) {
    $msg_type = 'info';
    $msg = htmlspecialchars($_GET['msg']);

    // Set message type based on message content
    if ($msg == 'password_reset') {
        $msg = 'Your password has been reset successfully. You can now login with your new password.';
        $msg_type = 'success';
    }

    echo "<div class='container mt-3'><div class='alert alert-{$msg_type} alert-dismissible fade show' role='alert'>
            {$msg}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Ensure $msg is always defined to avoid undefined variable warning
if (!isset($msg)) $msg = '';
?>
<style>
/* Professional Animated Background */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
}

/* Animated Gradient Background */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -3;
}

/* Floating Particles */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 20s infinite linear;
}

.particle:nth-child(1) { width: 20px; height: 20px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 15px; height: 15px; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { width: 25px; height: 25px; left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { width: 18px; height: 18px; left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { width: 22px; height: 22px; left: 50%; animation-delay: 8s; }
.particle:nth-child(6) { width: 16px; height: 16px; left: 60%; animation-delay: 10s; }
.particle:nth-child(7) { width: 24px; height: 24px; left: 70%; animation-delay: 12s; }
.particle:nth-child(8) { width: 19px; height: 19px; left: 80%; animation-delay: 14s; }
.particle:nth-child(9) { width: 21px; height: 21px; left: 90%; animation-delay: 16s; }
.particle:nth-child(10) { width: 17px; height: 17px; left: 95%; animation-delay: 18s; }

/* Geometric Shapes */
.geometric-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.shape {
    position: absolute;
    opacity: 0.1;
}

.shape-1 {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 20px;
    top: 10%;
    left: 5%;
    animation: rotate 20s linear infinite;
}

.shape-2 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #a8edea, #fed6e3);
    border-radius: 50%;
    top: 20%;
    right: 10%;
    animation: pulse 15s ease-in-out infinite;
}

.shape-3 {
    width: 120px;
    height: 60px;
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
    border-radius: 30px;
    bottom: 20%;
    left: 10%;
    animation: float 25s ease-in-out infinite;
}

.shape-4 {
    width: 90px;
    height: 90px;
    background: linear-gradient(45deg, #a8caba, #5d4e75);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    bottom: 15%;
    right: 15%;
    animation: rotate 30s linear infinite reverse;
}

/* Glass Morphism Login Card */
.login-container {
    position: relative;
    z-index: 10;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.login-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    padding: 3rem 2.5rem;
    width: 100%;
    max-width: 420px;
    animation: slideInUp 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.brand {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 2px;
    text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
    margin-bottom: 0.5rem;
    animation: glow 3s ease-in-out infinite alternate;
}

.subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 300;
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form Styling */
.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    color: white;
}

.input-group-text {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-right: none;
    border-radius: 12px 0 0 12px;
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.toggle-password {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-left: none;
    border-radius: 0 12px 12px 0;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.toggle-password:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.register-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.register-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.register-link:hover::after {
    width: 100%;
}

.register-link:hover {
    color: white;
}

.forgot-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes float {
    0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes glow {
    from { text-shadow: 0 0 30px rgba(102, 126, 234, 0.3); }
    to { text-shadow: 0 0 40px rgba(102, 126, 234, 0.6), 0 0 60px rgba(118, 75, 162, 0.3); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }

    .brand {
        font-size: 2rem;
    }

    .shape-1, .shape-2, .shape-3, .shape-4 {
        display: none;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 1.5rem 1rem;
    }

    .brand {
        font-size: 1.8rem;
    }
}

/* Alert Styling */
.alert {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    backdrop-filter: blur(10px);
}

.alert-danger {
    background: rgba(244, 63, 94, 0.1);
    border-color: rgba(244, 63, 94, 0.3);
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}
</style>
<!-- Animated Background Elements -->
<div class="animated-background"></div>

<!-- Floating Particles -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<!-- Geometric Shapes -->
<div class="geometric-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
    <div class="shape shape-4"></div>
</div>

<!-- Login Container -->
<div class="login-container">
    <div class="login-card">
        <div class="text-center mb-4">
            <div class="brand"><i class="fa fa-hotel me-2"></i>SmartHotel</div>
            <div class="subtitle">Sign in to your account</div>
        </div>

        <?php if ($msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fa fa-exclamation-circle me-2"></i><?= htmlspecialchars($msg) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <form method="post" action="login_process.php" autocomplete="off">
            <div class="mb-3">
                <label class="form-label" for="email">Email Address</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email address" required autofocus>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label" for="password">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fa fa-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                    <span class="input-group-text toggle-password" onclick="togglePassword()">
                        <i class="fa fa-eye"></i>
                    </span>
                </div>
            </div>

            <div class="mb-4 d-flex justify-content-between align-items-center">
                <a href="forgot_password.php" class="forgot-link">Forgot your password?</a>
            </div>

            <button class="btn btn-primary w-100 py-3 mb-3" type="submit">
                <i class="fa fa-sign-in-alt me-2"></i>Sign In
            </button>
        </form>

        <div class="text-center">
            <span style="color: rgba(255, 255, 255, 0.7);">Don't have an account?</span>
            <a href="register.php" class="register-link">Create Account</a>
        </div>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Enhanced Login Page Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add focus animations to form inputs
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.3s ease';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // Add hover effect to login card
    const loginCard = document.querySelector('.login-card');
    loginCard.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1)';
    });

    loginCard.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05)';
    });

    // Add ripple effect to button
    const button = document.querySelector('.btn-primary');
    button.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        this.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });

    // Create additional floating elements
    createFloatingElements();
});

function togglePassword() {
    var pwd = document.getElementById('password');
    var icon = event.currentTarget.querySelector('i');
    if (pwd.type === 'password') {
        pwd.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        pwd.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function createFloatingElements() {
    const container = document.querySelector('.particles');

    // Create additional animated elements
    for (let i = 0; i < 5; i++) {
        const element = document.createElement('div');
        element.className = 'floating-element';
        element.style.cssText = `
            position: absolute;
            width: ${Math.random() * 10 + 5}px;
            height: ${Math.random() * 10 + 5}px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            animation: float ${Math.random() * 10 + 15}s infinite linear;
            animation-delay: ${Math.random() * 5}s;
        `;
        container.appendChild(element);
    }
}

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .floating-element {
        pointer-events: none;
    }

    /* Enhanced hover effects */
    .login-card {
        transition: all 0.3s ease;
    }

    .input-group {
        transition: all 0.3s ease;
    }

    /* Smooth scrolling for mobile */
    html {
        scroll-behavior: smooth;
    }

    /* Additional mobile optimizations */
    @media (max-width: 480px) {
        .animated-background {
            background-size: 600% 600%;
        }

        .particle {
            display: none;
        }
    }
`;
document.head.appendChild(style);
</script>
