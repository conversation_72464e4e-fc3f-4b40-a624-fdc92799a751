<?php
// User Login Page
include 'includes/session.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: index.php');
    exit;
}

include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Display URL message if any (for backward compatibility)
if (isset($_GET['msg'])) {
    $msg_type = 'info';
    $msg = htmlspecialchars($_GET['msg']);

    // Set message type based on message content
    if ($msg == 'password_reset') {
        $msg = 'Your password has been reset successfully. You can now login with your new password.';
        $msg_type = 'success';
    }

    echo "<div class='container mt-3'><div class='alert alert-{$msg_type} alert-dismissible fade show' role='alert'>
            {$msg}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}
?>
<div class="container" style="margin-top:2rem;max-width:400px;">
    <h2 class="mb-4 text-center" style="font-weight:600;">User Login</h2>
    <form method="post" action="login_process.php" autocomplete="off">
        <div class="mb-3">
            <label class="form-label">Email</label>
            <input type="email" class="form-control" name="email" required autofocus>
        </div>
        <div class="mb-3">
            <label class="form-label">Password</label>
            <input type="password" class="form-control" name="password" required>
            <div class="text-end mt-1">
                <a href="forgot_password.php" class="text-decoration-none small">Forgot Password?</a>
            </div>
        </div>
        <button class="btn btn-primary w-100" type="submit">Login</button>
        <div class="text-center mt-3">
            Don't have an account? <a href="register.php">Register</a>
        </div>
    </form>
</div>
<?php include 'includes/footer.php'; ?>
