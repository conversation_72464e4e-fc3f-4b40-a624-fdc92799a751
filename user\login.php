<?php
// User Login Page
include 'includes/session.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: index.php');
    exit;
}

include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Display URL message if any (for backward compatibility)
if (isset($_GET['msg'])) {
    $msg_type = 'info';
    $msg = htmlspecialchars($_GET['msg']);

    // Set message type based on message content
    if ($msg == 'password_reset') {
        $msg = 'Your password has been reset successfully. You can now login with your new password.';
        $msg_type = 'success';
    }

    echo "<div class='container mt-3'><div class='alert alert-{$msg_type} alert-dismissible fade show' role='alert'>
            {$msg}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Ensure $msg is always defined to avoid undefined variable warning
if (!isset($msg)) $msg = '';
?>
<style>
/* Modern Login Background */
body {
    background: url('assets/images/hotels/gazebo_pools_and_restaurant.jpg') no-repeat center center fixed;
    background-size: cover;
    min-height: 100vh;
    position: relative;
}
body::before {
    content: '';
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(15,23,42,0.7); /* dark overlay */
    z-index: 0;
}
.login-card {
    background: rgba(255,255,255,0.95);
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    padding: 2.5rem 2rem 2rem 2rem;
    position: relative;
    z-index: 1;
    margin-top: 4rem;
    margin-bottom: 4rem;
    animation: fadeInUp 0.7s;
}
.brand {
    font-size: 2rem;
    font-weight: 800;
    color: #2563eb;
    letter-spacing: 1px;
    text-shadow: 1px 2px 8px rgba(37,99,235,0.08);
}
input.form-control {
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    font-size: 1.1rem;
    transition: border 0.2s;
}
input.form-control:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #2563eb22;
}
.input-group-text {
    background: #f1f5f9;
    border: none;
    border-radius: 8px 0 0 8px;
    color: #2563eb;
}
.toggle-password {
    cursor: pointer;
    background: #f1f5f9;
    border-radius: 0 8px 8px 0;
    border: none;
    color: #64748b;
}
.btn-primary {
    background: linear-gradient(90deg,#2563eb,#1e40af);
    border: none;
    font-weight: 600;
    font-size: 1.1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px #2563eb22;
    transition: background 0.2s;
}
.btn-primary:hover {
    background: linear-gradient(90deg,#1e40af,#2563eb);
}
.register-link {
    color: #2563eb;
    font-weight: 600;
    text-decoration: underline;
}
@media (max-width: 600px) {
    .login-card { padding: 1.5rem 0.5rem; }
}
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(40px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
<div class="container" style="margin-top:2rem;max-width:400px;position:relative;z-index:2;">
    <div class="login-card animate__animated animate__fadeInDown">
        <div class="text-center mb-4">
            <span class="brand"><i class="fa fa-hotel me-2"></i>SmartHotel</span>
            <div class="mt-2 mb-1 text-muted" style="font-size:1.1rem;">Sign in to your account</div>
        </div>
        <?php if ($msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fa fa-exclamation-circle me-2"></i><?= htmlspecialchars($msg) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <form method="post" action="login_process.php" autocomplete="off">
            <div class="mb-3">
                <label class="form-label" for="email">Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required autofocus>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label" for="password">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fa fa-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                    <span class="input-group-text toggle-password" onclick="togglePassword()"><i class="fa fa-eye"></i></span>
                </div>
            </div>
            <div class="mb-3 d-flex justify-content-between align-items-center">
                <a href="forgot_password.php" class="small text-decoration-none">Forgot password?</a>
            </div>
            <button class="btn btn-primary w-100 py-2 mb-2" type="submit">Login <i class="fa fa-arrow-right ms-1"></i></button>
        </form>
        <div class="text-center mt-3">
            <span>Don't have an account?</span> <a href="register.php" class="register-link">Register</a>
        </div>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function togglePassword() {
    var pwd = document.getElementById('password');
    var icon = event.currentTarget.querySelector('i');
    if (pwd.type === 'password') {
        pwd.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        pwd.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
