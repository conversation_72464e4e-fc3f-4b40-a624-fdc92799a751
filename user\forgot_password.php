<?php
// Forgot Password Page
include 'includes/header.php';
?>

<div class="container" style="margin-top:2rem;max-width:500px;">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-key"></i> Forgot Password</h4>
        </div>
        <div class="card-body">
            <?php if (isset($_GET['msg'])): ?>
                <?php if ($_GET['msg'] == 'success'): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Password reset link has been sent to your email address.
                    </div>
                <?php elseif ($_GET['msg'] == 'error'): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> Email address not found in our records.
                    </div>
                <?php elseif ($_GET['msg'] == 'mail_error'): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> There was an error sending the password reset email. Please try again later.
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <p class="mb-4">Enter your email address below and we'll send you a link to reset your password.</p>
            
            <form method="post" action="forgot_password_process.php">
                <div class="mb-3">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-control" name="email" required>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Reset Link
                    </button>
                    <a href="login.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Login
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    padding: 1.25rem;
}

.alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.form-control {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
</style>

<?php include 'includes/footer.php'; ?>
