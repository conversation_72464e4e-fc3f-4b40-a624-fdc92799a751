# <PERSON><PERSON><PERSON>ailer Troubleshooting Guide

This guide will help you troubleshoot and fix issues with <PERSON><PERSON><PERSON>ail<PERSON> in your application.

## Common Issues and Solutions

### 1. SMTP Connection Issues

If you're having trouble connecting to the SMTP server, try the following:

- **Check SMTP Host and Port**: Make sure the SMTP host and port are correct. Common settings:
  - Gmail: `smtp.gmail.com`, Port: `587` (TLS) or `465` (SSL)
  - Outlook/Hotmail: `smtp.office365.com`, Port: `587` (TLS)
  - Yahoo: `smtp.mail.yahoo.com`, Port: `587` (TLS) or `465` (SSL)

- **Check Security Settings**: Make sure you're using the correct security setting (TLS, SSL, or None).
  - For Gmail, use TLS on port 587.
  - For most modern email providers, TLS is recommended.

- **Check Authentication**: Make sure your username and password are correct.
  - For Gmail, you may need to use an "App Password" instead of your regular password.
  - To create an App Password for Gmail:
    1. Go to your Google Account settings
    2. Select "Security"
    3. Under "Signing in to Google," select "App passwords"
    4. Generate a new app password for "Mail" and "Other (Custom name)"

- **Check Firewall Settings**: Make sure your server's firewall allows outgoing connections on the SMTP port.

### 2. Email Not Being Sent

If the connection is successful but emails aren't being sent, check the following:

- **From Email Address**: Make sure the "From" email address is valid and matches the SMTP account you're using.
  - Some SMTP servers (like Gmail) require the "From" address to match the account you're authenticating with.

- **Email Content**: Make sure the email content is valid.
  - Check for any special characters that might be causing issues.
  - Make sure the HTML is properly formatted.

- **Recipient Address**: Make sure the recipient email address is valid.

### 3. PHP Configuration Issues

- **Required Extensions**: Make sure the following PHP extensions are enabled:
  - `openssl`
  - `mbstring`
  - `iconv`

- **PHP Version**: PHPMailer requires PHP 5.5 or higher. Make sure your PHP version is compatible.

## Debugging Tools

We've provided several debugging tools to help you diagnose issues:

1. **mailer_debug.php**: This file provides detailed information about your PHP configuration and allows you to test email sending with debug output.
   - Access it at: `/includes/mailer_debug.php`

2. **test_mailer.php**: A simple script to test PHPMailer directly without using the database settings.
   - Access it at: `/test_mailer.php`

3. **Debug Mode**: You can enable debug mode in the mailer.php file by uncommenting the debug line:
   ```php
   // Uncomment the line below to enable debugging
   // $mail->SMTPDebug = SMTP::DEBUG_SERVER; // Output debug info
   ```

## Specific Error Solutions

### "SMTP connect() failed"

This usually indicates a connection issue. Check:
- SMTP host and port
- Firewall settings
- Network connectivity

### "Authentication failed"

This indicates an issue with your username or password. Check:
- Username and password are correct
- For Gmail, try using an App Password
- Make sure the account doesn't have 2FA without an App Password

### "Could not instantiate mail function"

This indicates an issue with your PHP mail configuration. Try:
- Using SMTP instead of the PHP mail function
- Check your PHP mail settings in php.ini

### "Invalid address"

This indicates an issue with an email address. Check:
- From email address is valid
- Recipient email address is valid
- Email addresses are properly formatted

## Email Settings in Admin Panel

You can configure email settings in the admin panel:
1. Go to Admin > Email Settings
2. Enter your SMTP settings
3. Click "Save Settings"
4. Use the "Test Email" feature to verify your settings

## Manual Configuration

If you need to manually configure the email settings, you can edit the following files:

1. **includes/mailer.php**: The main mailer implementation
2. **admin/email_settings.php**: The admin panel email settings page

## Need More Help?

If you're still having issues after trying these solutions, please:
1. Enable debug mode
2. Capture the debug output
3. Contact support with the debug output and a description of the issue

## Security Considerations

- Never store SMTP passwords in plain text in your code
- Use environment variables or a secure configuration system for sensitive information
- Consider using OAuth2 for authentication with providers that support it (like Gmail)
- Keep PHPMailer updated to the latest version to avoid security vulnerabilities
