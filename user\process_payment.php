<?php
// User Payment Processing and Email Receipt (PHPMailer)
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';
session_start();
$user_id = $_SESSION['user_id'] ?? 0;
$booking_id = (int)($_POST['booking_id'] ?? 0);
$payment_method_id = (int)($_POST['payment_method_id'] ?? 0);
if (!$user_id || !$booking_id || !$payment_method_id) { header('Location: my_bookings.php?msg=Invalid+payment+data'); exit; }
// Validate booking and payment method
$res = $mysqli->query("SELECT b.*, u.email, u.first_name, u.last_name, r.room_number, h.hotel_name, pm.method_name FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id JOIN payment_methods pm ON pm.payment_method_id=$payment_method_id WHERE b.booking_id=$booking_id AND b.user_id=$user_id AND b.payment_status='pending' LIMIT 1");
$row = $res->fetch_assoc();
if (!$row) { header('Location: my_bookings.php?msg=Invalid+or+already+paid'); exit; }
// Mark as paid
$mysqli->query("UPDATE bookings SET payment_status='paid' WHERE booking_id=$booking_id");
// Send email receipt
$mail = new PHPMailer(true);
try {
    $mail->isSMTP();
    $mail->Host = 'smtp.example.com'; // TODO: Change to your SMTP server
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // TODO: Change to your SMTP username
    $mail->Password = 'yourpassword';   // TODO: Change to your SMTP password
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    $mail->setFrom('<EMAIL>', 'SmartHotel');
    $mail->addAddress($row['email'], $row['first_name'].' '.$row['last_name']);
    $mail->isHTML(true);
    $mail->Subject = 'Payment Receipt - Booking #'.$booking_id;
    $mail->Body = '<div style="font-family:Arial,sans-serif;max-width:600px;margin:0 auto;padding:24px;background:#f8fafc;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.07);">'
        .'<h2 style="color:#2563eb;font-weight:700;margin-bottom:16px;"><i class="fas fa-receipt"></i> Booking Receipt</h2>'
        .'<p style="font-size:1.1em;">Dear <b>'.htmlspecialchars($row['first_name']).'</b>,<br>Thank you for your payment. Here is your booking receipt:</p>'
        .'<table style="width:100%;margin-bottom:16px;font-size:1em;">'
        .'<tr><td><b>Booking ID:</b></td><td>#'.$booking_id.'</td></tr>'
        .'<tr><td><b>Hotel:</b></td><td>'.htmlspecialchars($row['hotel_name']).'</td></tr>'
        .'<tr><td><b>Room:</b></td><td>'.htmlspecialchars($row['room_number']).'</td></tr>'
        .'<tr><td><b>Check-in:</b></td><td>'.$row['check_in_date'].'</td></tr>'
        .'<tr><td><b>Check-out:</b></td><td>'.$row['check_out_date'].'</td></tr>'
        .'<tr><td><b>Total Paid:</b></td><td>₱'.number_format($row['total_price'],2).'</td></tr>'
        .'<tr><td><b>Payment Method:</b></td><td>'.htmlspecialchars($row['method_name']).'</td></tr>'
        .'</table>'
        .'<h4 style="margin-top:24px;margin-bottom:8px;">Payment Methods</h4>'
        .'<ul style="padding-left:18px;margin-bottom:24px;">'
        .'<li>Credit/Debit Card</li>'
        .'<li>Bank Transfer</li>'
        .'<li>GCash/PayMaya</li>'
        .'<li>Cash on Arrival</li>'
        .'</ul>'
        .'<div style="margin-top:24px;font-size:1.1em;color:#2563eb;font-weight:600;">Successfully Booked! We look forward to welcoming you.<br><br>SmartHotel Team</div>'
        .'</div>';
    $mail->send();
    header('Location: receipt.php?id=' . $booking_id . '&success=1');
    exit;
} catch (Exception $e) {
    header('Location: receipt.php?id=' . $booking_id . '&success=0');
    exit;
}
