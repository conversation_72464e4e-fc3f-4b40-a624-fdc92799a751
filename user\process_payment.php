<?php
/**
 * Payment Processing System - 100% Success Guaranteed
 * Handles payment processing with comprehensive error handling and logging
 */

// Include required files
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';

// Start session and validate user
session_start();
$user_id = $_SESSION['user_id'] ?? 0;

// Validate input parameters
$booking_id = (int)($_POST['booking_id'] ?? 0);
$payment_method_id = (int)($_POST['payment_method_id'] ?? 0);

// Capture payment method specific data
$gcash_reference = trim($_POST['gcash_reference'] ?? '');
$bank_reference = trim($_POST['bank_reference'] ?? '');
$card_last_four = '';

// Extract card last 4 digits if credit card
if (!empty($_POST['card_number'])) {
    $card_number = preg_replace('/\D/', '', $_POST['card_number']);
    if (strlen($card_number) >= 4) {
        $card_last_four = substr($card_number, -4);
    }
}

// Input validation
if (!$user_id) {
    header('Location: login.php?msg=Please+login+to+continue');
    exit;
}

if (!$booking_id || !$payment_method_id) {
    header('Location: my_bookings.php?msg=Invalid+payment+data');
    exit;
}

// Begin transaction for data consistency
$mysqli->begin_transaction();

try {
    // Validate booking and get all required data
    $stmt = $mysqli->prepare("
        SELECT b.*, u.email, u.first_name, u.last_name,
               r.room_number, h.hotel_name, h.phone as hotel_phone,
               pm.method_name, rt.type_name
        FROM bookings b
        JOIN users u ON b.user_id = u.user_id
        JOIN rooms r ON b.room_id = r.room_id
        JOIN hotels h ON r.hotel_id = h.hotel_id
        JOIN payment_methods pm ON pm.payment_method_id = ?
        JOIN room_types rt ON r.room_type_id = rt.room_type_id
        WHERE b.booking_id = ? AND b.user_id = ? AND b.payment_status = 'pending'
        LIMIT 1
    ");

    $stmt->bind_param('iii', $payment_method_id, $booking_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $booking_data = $result->fetch_assoc();
    $stmt->close();

    if (!$booking_data) {
        throw new Exception('Invalid booking or booking already paid');
    }

    // Generate unique transaction ID
    $transaction_id = 'TXN_' . date('YmdHis') . '_' . $booking_id . '_' . rand(1000, 9999);

    // Prepare payment reference for logging (optional)
    $payment_reference = '';
    if (!empty($gcash_reference)) {
        $payment_reference = "GCash Ref: " . $gcash_reference;
    } elseif (!empty($card_last_four)) {
        $payment_reference = "Card: ****" . $card_last_four;
    }

    // Insert payment record
    $stmt = $mysqli->prepare("
        INSERT INTO payments (booking_id, user_id, payment_method_id, amount,
                            payment_status, transaction_id, payment_date)
        VALUES (?, ?, ?, ?, 'completed', ?, NOW())
    ");

    $stmt->bind_param('iiiis', $booking_id, $user_id, $payment_method_id,
                     $booking_data['total_price'], $transaction_id);

    if (!$stmt->execute()) {
        throw new Exception('Failed to record payment: ' . $stmt->error);
    }
    $payment_id = $mysqli->insert_id;
    $stmt->close();

    // Update booking status
    $stmt = $mysqli->prepare("
        UPDATE bookings
        SET payment_status = 'paid', booking_status = 'confirmed', updated_at = NOW()
        WHERE booking_id = ?
    ");

    $stmt->bind_param('i', $booking_id);

    if (!$stmt->execute()) {
        throw new Exception('Failed to update booking status: ' . $stmt->error);
    }
    $stmt->close();

    // Commit transaction
    $mysqli->commit();

    // Send email receipt (non-blocking - if it fails, payment still succeeds)
    $email_sent = sendPaymentReceipt($booking_data, $transaction_id, $payment_id);

    // Redirect to success page
    $success_msg = $email_sent ? 'Payment+successful+and+receipt+sent' : 'Payment+successful';
    header("Location: receipt.php?id={$booking_id}&success=1&msg={$success_msg}&txn={$transaction_id}");
    exit;

} catch (Exception $e) {
    // Rollback transaction on error
    $mysqli->rollback();

    // Log error for debugging
    error_log("Payment processing error for booking {$booking_id}: " . $e->getMessage());

    // Redirect with error message
    $error_msg = urlencode($e->getMessage());
    header("Location: my_bookings.php?msg=Payment+failed:+{$error_msg}");
    exit;
}

/**
 * Send payment receipt email
 * Returns true if successful, false otherwise
 */
function sendPaymentReceipt($booking_data, $transaction_id, $payment_id) {
    global $mysqli;

    try {
        // Get email settings from database
        $settings_result = $mysqli->query("SELECT * FROM system_settings LIMIT 1");

        if (!$settings_result || $settings_result->num_rows === 0) {
            // Use default settings if none configured
            $settings = [
                'smtp_host' => 'smtp.gmail.com',
                'smtp_port' => 587,
                'smtp_secure' => 'tls',
                'smtp_username' => '<EMAIL>',
                'smtp_password' => 'ggqp rnqr rnqr rnqr',
                'from_email' => '<EMAIL>',
                'from_name' => 'SmartHotel'
            ];
        } else {
            $settings = $settings_result->fetch_assoc();
        }

        // Skip email if not properly configured
        if (empty($settings['smtp_host']) || $settings['smtp_host'] === 'smtp.example.com' ||
            empty($settings['from_email']) || $settings['from_email'] === '<EMAIL>') {
            return false;
        }

        $mail = new PHPMailer(true);

        // SMTP Configuration
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->Port = intval($settings['smtp_port']);
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];

        if ($settings['smtp_secure'] === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($settings['smtp_secure'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = '';
            $mail->SMTPAutoTLS = false;
        }

        // Additional settings for better compatibility
        $mail->Timeout = 60;
        $mail->SMTPKeepAlive = false;
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Email content
        $mail->setFrom($settings['from_email'], $settings['from_name']);
        $mail->addAddress($booking_data['email'], $booking_data['first_name'] . ' ' . $booking_data['last_name']);
        $mail->isHTML(true);
        $mail->Subject = 'SmartHotel Booking Receipt - Confirmation #' . $booking_data['booking_id'];

        // Calculate nights
        $checkin = new DateTime($booking_data['check_in_date']);
        $checkout = new DateTime($booking_data['check_out_date']);
        $nights = $checkin->diff($checkout)->days;

        // Get amenities for this booking
        $amenities = getBookingAmenities($booking_data['booking_id']);

        $mail->Body = generateSmartHotelEmailTemplate($booking_data, $transaction_id, $nights, $amenities);
        $mail->AltBody = generateSmartHotelPlainTextEmail($booking_data, $transaction_id, $nights, $amenities);

        $mail->send();
        return true;

    } catch (Exception $e) {
        // Log email error but don't fail the payment
        error_log("Email sending failed for payment {$payment_id}: " . $e->getMessage());
        return false;
    }
}

/**
 * Get booking amenities with pricing
 */
function getBookingAmenities($booking_id) {
    global $mysqli;
    include_once 'includes/amenity_pricing.php';

    $amenities = [];
    $query = "SELECT ba.amenity_id, a.amenity_name, ba.custom_name, ba.custom_price
              FROM booking_amenities ba
              LEFT JOIN amenities a ON ba.amenity_id = a.amenity_id
              WHERE ba.booking_id = ?";

    $stmt = $mysqli->prepare($query);
    $stmt->bind_param('i', $booking_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $name = $row['custom_name'] ?: $row['amenity_name'];
        $price = $row['custom_price'] !== null ? $row['custom_price'] : getAmenityPrice($row['amenity_id']);

        if ($name && $price !== null) {
            $amenities[] = [
                'name' => $name,
                'price' => $price
            ];
        }
    }

    $stmt->close();
    return $amenities;
}

/**
 * Generate SmartHotel branded HTML email template
 */
function generateSmartHotelEmailTemplate($booking_data, $transaction_id, $nights, $amenities) {
    // Calculate room price (total minus amenities)
    $room_price = floatval($booking_data['total_price']);
    $amenities_total = 0;

    foreach ($amenities as $amenity) {
        $amenities_total += floatval($amenity['price']);
    }

    $room_price -= $amenities_total;

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SmartHotel Booking Receipt</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center;">
                <h1 style="margin: 0; font-size: 32px; font-weight: bold;">SmartHotel</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Experience the future of hotel booking</p>
            </div>

            <!-- Success Message -->
            <div style="background-color: #d4edda; border-left: 4px solid #28a745; padding: 20px; margin: 0;">
                <h2 style="margin: 0; color: #155724; font-size: 24px;">Successfully Booked!</h2>
                <p style="margin: 5px 0 0 0; color: #155724; font-size: 16px;">Thank you for your reservation.</p>
            </div>

            <!-- Booking Receipt -->
            <div style="padding: 30px 20px;">
                <h3 style="margin: 0 0 20px 0; color: #333; font-size: 22px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Booking Receipt</h3>

                <!-- Booking Details -->
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Booking ID:</td>
                            <td style="padding: 8px 0; color: #666;">#' . htmlspecialchars($booking_data['booking_id']) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Name:</td>
                            <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($booking_data['first_name'] . ' ' . $booking_data['last_name']) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Email:</td>
                            <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($booking_data['email']) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Hotel:</td>
                            <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($booking_data['hotel_name']) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Room Number:</td>
                            <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($booking_data['room_number']) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Check-in:</td>
                            <td style="padding: 8px 0; color: #666;">' . date('Y-m-d', strtotime($booking_data['check_in_date'])) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Check-out:</td>
                            <td style="padding: 8px 0; color: #666;">' . date('Y-m-d', strtotime($booking_data['check_out_date'])) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">Status:</td>
                            <td style="padding: 8px 0; color: #28a745; font-weight: bold;">Confirmed</td>
                        </tr>
                    </table>
                </div>

                <!-- Charges Breakdown -->
                <div style="background-color: #fff; border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 25px;">
                    <div style="background-color: #667eea; color: white; padding: 15px; border-radius: 8px 8px 0 0;">
                        <h4 style="margin: 0; font-size: 18px;">Charges</h4>
                    </div>
                    <div style="padding: 20px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td style="padding: 12px 0; font-weight: bold; color: #333;">Room Price</td>
                                <td style="padding: 12px 0; text-align: right; color: #333;">₱' . number_format($room_price, 2) . '</td>
                            </tr>';

    // Add amenities to the charges
    foreach ($amenities as $amenity) {
        $html .= '
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td style="padding: 12px 0; color: #666;">' . htmlspecialchars($amenity['name']) . '</td>
                                <td style="padding: 12px 0; text-align: right; color: #666;">₱' . number_format($amenity['price'], 2) . '</td>
                            </tr>';
    }

    $html .= '
                            <tr style="border-top: 2px solid #667eea;">
                                <td style="padding: 15px 0; font-weight: bold; font-size: 18px; color: #333;">Total</td>
                                <td style="padding: 15px 0; text-align: right; font-weight: bold; font-size: 18px; color: #667eea;">₱' . number_format($booking_data['total_price'], 2) . '</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Thank You Message -->
                <div style="text-align: center; background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
                    <h3 style="margin: 0 0 10px 0; color: #667eea; font-size: 20px;">Thank you for booking with SmartHotel!</h3>
                    <p style="margin: 0; color: #666; font-size: 16px;">Please check your email for your official receipt and further instructions. We look forward to welcoming you!</p>
                </div>

                <!-- Payment Information -->
                <div style="background-color: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
                    <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 16px;">Payment Information</h4>
                    <p style="margin: 5px 0; color: #6c757d; font-size: 14px;"><strong>Payment Method:</strong> ' . htmlspecialchars($booking_data['method_name']) . '</p>
                    <p style="margin: 5px 0; color: #6c757d; font-size: 14px;"><strong>Transaction ID:</strong> ' . htmlspecialchars($transaction_id) . '</p>
                    <p style="margin: 5px 0; color: #6c757d; font-size: 14px;"><strong>Payment Date:</strong> ' . date('F j, Y \a\t g:i A') . '</p>
                </div>
            </div>

            <!-- Footer -->
            <div style="background-color: #343a40; color: white; padding: 30px 20px; text-align: center;">
                <h3 style="margin: 0 0 10px 0; font-size: 20px;">SmartHotel</h3>
                <p style="margin: 0 0 15px 0; font-size: 14px; opacity: 0.8;">Experience the future of hotel booking with smart rooms, sustainable stays, and exclusive innovations.</p>

                <div style="margin: 20px 0; padding: 20px 0; border-top: 1px solid #495057; border-bottom: 1px solid #495057;">
                    <p style="margin: 5px 0; font-size: 14px;">📍 123 Hotel Street, City, Country</p>
                    <p style="margin: 5px 0; font-size: 14px;">📞 ****** 567 8900</p>
                    <p style="margin: 5px 0; font-size: 14px;">✉️ <EMAIL></p>
                    <p style="margin: 5px 0; font-size: 14px;">🕒 24/7 Customer Support</p>
                </div>

                <p style="margin: 0; font-size: 12px; opacity: 0.7;">
                    © ' . date('Y') . ' SmartHotel — Experience the Future of Booking<br>
                    Made By Johnny Guzon Polloso
                </p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}

/**
 * Generate SmartHotel plain text email
 */
function generateSmartHotelPlainTextEmail($booking_data, $transaction_id, $nights, $amenities) {
    $text = "SMARTHOTEL BOOKING RECEIPT\n";
    $text .= "==========================\n\n";
    $text .= "Successfully Booked! Thank you for your reservation.\n\n";

    $text .= "BOOKING DETAILS:\n";
    $text .= "Booking ID: #" . $booking_data['booking_id'] . "\n";
    $text .= "Name: " . $booking_data['first_name'] . " " . $booking_data['last_name'] . "\n";
    $text .= "Email: " . $booking_data['email'] . "\n";
    $text .= "Hotel: " . $booking_data['hotel_name'] . "\n";
    $text .= "Room Number: " . $booking_data['room_number'] . "\n";
    $text .= "Check-in: " . date('Y-m-d', strtotime($booking_data['check_in_date'])) . "\n";
    $text .= "Check-out: " . date('Y-m-d', strtotime($booking_data['check_out_date'])) . "\n";
    $text .= "Status: Confirmed\n\n";

    $text .= "CHARGES:\n";

    // Calculate room price
    $room_price = floatval($booking_data['total_price']);
    $amenities_total = 0;
    foreach ($amenities as $amenity) {
        $amenities_total += floatval($amenity['price']);
    }
    $room_price -= $amenities_total;

    $text .= "Room Price: ₱" . number_format($room_price, 2) . "\n";

    foreach ($amenities as $amenity) {
        $text .= $amenity['name'] . ": ₱" . number_format($amenity['price'], 2) . "\n";
    }

    $text .= "Total: ₱" . number_format($booking_data['total_price'], 2) . "\n\n";

    $text .= "PAYMENT INFORMATION:\n";
    $text .= "Payment Method: " . $booking_data['method_name'] . "\n";
    $text .= "Transaction ID: " . $transaction_id . "\n";
    $text .= "Payment Date: " . date('F j, Y \a\t g:i A') . "\n\n";

    $text .= "Thank you for booking with SmartHotel!\n";
    $text .= "Please check your email for your official receipt and further instructions.\n";
    $text .= "We look forward to welcoming you!\n\n";

    $text .= "SMARTHOTEL\n";
    $text .= "Experience the future of hotel booking\n";
    $text .= "123 Hotel Street, City, Country\n";
    $text .= "****** 567 8900\n";
    $text .= "<EMAIL>\n";
    $text .= "24/7 Customer Support\n\n";

    $text .= "© " . date('Y') . " SmartHotel — Experience the Future of Booking\n";
    $text .= "Made By Johnny Guzon Polloso\n";

    return $text;
}
