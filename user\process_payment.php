<?php
/**
 * Payment Processing System - 100% Success Guaranteed
 * Handles payment processing with comprehensive error handling and logging
 */

// Include required files
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';

// Start session and validate user
session_start();
$user_id = $_SESSION['user_id'] ?? 0;

// Validate input parameters
$booking_id = (int)($_POST['booking_id'] ?? 0);
$payment_method_id = (int)($_POST['payment_method_id'] ?? 0);

// Capture payment method specific data
$gcash_reference = trim($_POST['gcash_reference'] ?? '');
$bank_reference = trim($_POST['bank_reference'] ?? '');
$card_last_four = '';

// Extract card last 4 digits if credit card
if (!empty($_POST['card_number'])) {
    $card_number = preg_replace('/\D/', '', $_POST['card_number']);
    if (strlen($card_number) >= 4) {
        $card_last_four = substr($card_number, -4);
    }
}

// Input validation
if (!$user_id) {
    header('Location: login.php?msg=Please+login+to+continue');
    exit;
}

if (!$booking_id || !$payment_method_id) {
    header('Location: my_bookings.php?msg=Invalid+payment+data');
    exit;
}

// Begin transaction for data consistency
$mysqli->begin_transaction();

try {
    // Validate booking and get all required data
    $stmt = $mysqli->prepare("
        SELECT b.*, u.email, u.first_name, u.last_name,
               r.room_number, h.hotel_name, h.phone as hotel_phone,
               pm.method_name, rt.type_name
        FROM bookings b
        JOIN users u ON b.user_id = u.user_id
        JOIN rooms r ON b.room_id = r.room_id
        JOIN hotels h ON r.hotel_id = h.hotel_id
        JOIN payment_methods pm ON pm.payment_method_id = ?
        JOIN room_types rt ON r.room_type_id = rt.room_type_id
        WHERE b.booking_id = ? AND b.user_id = ? AND b.payment_status = 'pending'
        LIMIT 1
    ");

    $stmt->bind_param('iii', $payment_method_id, $booking_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $booking_data = $result->fetch_assoc();
    $stmt->close();

    if (!$booking_data) {
        throw new Exception('Invalid booking or booking already paid');
    }

    // Generate unique transaction ID
    $transaction_id = 'TXN_' . date('YmdHis') . '_' . $booking_id . '_' . rand(1000, 9999);

    // Prepare payment notes based on method
    $payment_notes = '';
    if (!empty($gcash_reference)) {
        $payment_notes = "GCash Reference: " . $gcash_reference;
    } elseif (!empty($bank_reference)) {
        $payment_notes = "Bank Reference: " . $bank_reference;
    } elseif (!empty($card_last_four)) {
        $payment_notes = "Card ending in: ****" . $card_last_four;
    }

    // Add method-specific notes
    if ($booking_data['method_name'] === 'Pay at Hotel') {
        $payment_notes = "Payment to be made at hotel front desk during check-in";
    } elseif ($booking_data['method_name'] === 'Cash on Arrival') {
        $payment_notes = "Cash payment to be made upon arrival at hotel";
    }

    // Insert payment record
    $stmt = $mysqli->prepare("
        INSERT INTO payments (booking_id, user_id, payment_method_id, amount,
                            payment_status, transaction_id, payment_date, notes)
        VALUES (?, ?, ?, ?, 'completed', ?, NOW(), ?)
    ");

    $stmt->bind_param('iiiiss', $booking_id, $user_id, $payment_method_id,
                     $booking_data['total_price'], $transaction_id, $payment_notes);

    if (!$stmt->execute()) {
        throw new Exception('Failed to record payment: ' . $stmt->error);
    }
    $payment_id = $mysqli->insert_id;
    $stmt->close();

    // Update booking status
    $stmt = $mysqli->prepare("
        UPDATE bookings
        SET payment_status = 'paid', booking_status = 'confirmed', updated_at = NOW()
        WHERE booking_id = ?
    ");

    $stmt->bind_param('i', $booking_id);

    if (!$stmt->execute()) {
        throw new Exception('Failed to update booking status: ' . $stmt->error);
    }
    $stmt->close();

    // Commit transaction
    $mysqli->commit();

    // Send email receipt (non-blocking - if it fails, payment still succeeds)
    $email_sent = sendPaymentReceipt($booking_data, $transaction_id, $payment_id);

    // Redirect to success page
    $success_msg = $email_sent ? 'Payment+successful+and+receipt+sent' : 'Payment+successful';
    header("Location: receipt.php?id={$booking_id}&success=1&msg={$success_msg}&txn={$transaction_id}");
    exit;

} catch (Exception $e) {
    // Rollback transaction on error
    $mysqli->rollback();

    // Log error for debugging
    error_log("Payment processing error for booking {$booking_id}: " . $e->getMessage());

    // Redirect with error message
    $error_msg = urlencode($e->getMessage());
    header("Location: my_bookings.php?msg=Payment+failed:+{$error_msg}");
    exit;
}

/**
 * Send payment receipt email
 * Returns true if successful, false otherwise
 */
function sendPaymentReceipt($booking_data, $transaction_id, $payment_id) {
    global $mysqli;

    try {
        // Get email settings from database
        $settings_result = $mysqli->query("SELECT * FROM system_settings LIMIT 1");

        if (!$settings_result || $settings_result->num_rows === 0) {
            // Use default settings if none configured
            $settings = [
                'smtp_host' => 'smtp.gmail.com',
                'smtp_port' => 587,
                'smtp_secure' => 'tls',
                'smtp_username' => '<EMAIL>',
                'smtp_password' => 'ggqp rnqr rnqr rnqr',
                'from_email' => '<EMAIL>',
                'from_name' => 'Hotel Admin'
            ];
        } else {
            $settings = $settings_result->fetch_assoc();
        }

        // Skip email if not properly configured
        if (empty($settings['smtp_host']) || $settings['smtp_host'] === 'smtp.example.com' ||
            empty($settings['from_email']) || $settings['from_email'] === '<EMAIL>') {
            return false;
        }

        $mail = new PHPMailer(true);

        // SMTP Configuration
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->Port = intval($settings['smtp_port']);
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];

        if ($settings['smtp_secure'] === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($settings['smtp_secure'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = '';
            $mail->SMTPAutoTLS = false;
        }

        // Additional settings for better compatibility
        $mail->Timeout = 60;
        $mail->SMTPKeepAlive = false;
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Email content
        $mail->setFrom($settings['from_email'], $settings['from_name']);
        $mail->addAddress($booking_data['email'], $booking_data['first_name'] . ' ' . $booking_data['last_name']);
        $mail->isHTML(true);
        $mail->Subject = 'Payment Receipt - Booking #' . $booking_data['booking_id'];

        // Calculate nights
        $checkin = new DateTime($booking_data['check_in_date']);
        $checkout = new DateTime($booking_data['check_out_date']);
        $nights = $checkin->diff($checkout)->days;

        $mail->Body = generateEmailTemplate($booking_data, $transaction_id, $nights);
        $mail->AltBody = generatePlainTextEmail($booking_data, $transaction_id, $nights);

        $mail->send();
        return true;

    } catch (Exception $e) {
        // Log email error but don't fail the payment
        error_log("Email sending failed for payment {$payment_id}: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate HTML email template
 */
function generateEmailTemplate($booking_data, $transaction_id, $nights) {
    return '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 10px; background-color: #ffffff;">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #4e73df; margin: 0; font-size: 28px;">Payment Confirmation</h1>
            <p style="color: #858796; margin: 5px 0 0 0; font-size: 16px;">Thank you for your payment!</p>
        </div>

        <div style="background: linear-gradient(135deg, #4e73df 0%, #224abe 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="margin: 0 0 10px 0; font-size: 20px;">Booking Confirmed</h2>
            <p style="margin: 0; font-size: 16px; opacity: 0.9;">Your reservation is now confirmed and paid in full.</p>
        </div>

        <div style="background-color: #f8f9fc; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="margin-top: 0; color: #4e73df; font-size: 18px;">Booking Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; font-weight: bold; color: #5a5c69;">Booking ID:</td>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; color: #5a5c69;">#' . htmlspecialchars($booking_data['booking_id']) . '</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; font-weight: bold; color: #5a5c69;">Hotel:</td>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; color: #5a5c69;">' . htmlspecialchars($booking_data['hotel_name']) . '</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; font-weight: bold; color: #5a5c69;">Room:</td>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; color: #5a5c69;">' . htmlspecialchars($booking_data['room_number']) . ' (' . htmlspecialchars($booking_data['type_name']) . ')</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; font-weight: bold; color: #5a5c69;">Check-in:</td>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; color: #5a5c69;">' . date('F j, Y', strtotime($booking_data['check_in_date'])) . '</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; font-weight: bold; color: #5a5c69;">Check-out:</td>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; color: #5a5c69;">' . date('F j, Y', strtotime($booking_data['check_out_date'])) . '</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; font-weight: bold; color: #5a5c69;">Duration:</td>
                    <td style="padding: 8px 0; border-bottom: 1px solid #e3e6f0; color: #5a5c69;">' . $nights . ' night' . ($nights > 1 ? 's' : '') . '</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #5a5c69; font-size: 16px;">Total Paid:</td>
                    <td style="padding: 8px 0; color: #1cc88a; font-weight: bold; font-size: 16px;">₱' . number_format($booking_data['total_price'], 2) . '</td>
                </tr>
            </table>
        </div>

        <div style="background-color: #e2e6ea; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
            <h4 style="margin-top: 0; color: #6c757d; font-size: 14px;">Payment Information</h4>
            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;"><strong>Payment Method:</strong> ' . htmlspecialchars($booking_data['method_name']) . '</p>
            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;"><strong>Transaction ID:</strong> ' . htmlspecialchars($transaction_id) . '</p>
            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;"><strong>Payment Date:</strong> ' . date('F j, Y \a\t g:i A') . '</p>
        </div>

        <div style="text-align: center; margin: 25px 0;">
            <p style="color: #5a5c69; font-size: 16px; margin-bottom: 15px;">We look forward to welcoming you!</p>
            <p style="color: #858796; font-size: 14px; margin: 0;">If you have any questions, please contact the hotel directly.</p>
            ' . (!empty($booking_data['hotel_phone']) ? '<p style="color: #858796; font-size: 14px; margin: 5px 0 0 0;">Hotel Phone: ' . htmlspecialchars($booking_data['hotel_phone']) . '</p>' : '') . '
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e3e6f0;">
            <p style="color: #858796; font-size: 12px; margin: 0;">
                &copy; ' . date('Y') . ' Hotel Booking System. This is an automated message.
            </p>
        </div>
    </div>';
}

/**
 * Generate plain text email
 */
function generatePlainTextEmail($booking_data, $transaction_id, $nights) {
    return "PAYMENT CONFIRMATION\n\n" .
           "Dear " . $booking_data['first_name'] . " " . $booking_data['last_name'] . ",\n\n" .
           "Thank you for your payment! Your booking is now confirmed.\n\n" .
           "BOOKING DETAILS:\n" .
           "Booking ID: #" . $booking_data['booking_id'] . "\n" .
           "Hotel: " . $booking_data['hotel_name'] . "\n" .
           "Room: " . $booking_data['room_number'] . " (" . $booking_data['type_name'] . ")\n" .
           "Check-in: " . date('F j, Y', strtotime($booking_data['check_in_date'])) . "\n" .
           "Check-out: " . date('F j, Y', strtotime($booking_data['check_out_date'])) . "\n" .
           "Duration: " . $nights . " night" . ($nights > 1 ? 's' : '') . "\n" .
           "Total Paid: ₱" . number_format($booking_data['total_price'], 2) . "\n\n" .
           "PAYMENT INFORMATION:\n" .
           "Payment Method: " . $booking_data['method_name'] . "\n" .
           "Transaction ID: " . $transaction_id . "\n" .
           "Payment Date: " . date('F j, Y \a\t g:i A') . "\n\n" .
           "We look forward to welcoming you!\n\n" .
           "Hotel Booking System";
}
