<?php
// User Payment Processing and Email Receipt (PHPMailer)
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';
session_start();
$user_id = $_SESSION['user_id'] ?? 0;
$booking_id = (int)($_POST['booking_id'] ?? 0);
$payment_method_id = (int)($_POST['payment_method_id'] ?? 0);
if (!$user_id || !$booking_id || !$payment_method_id) { header('Location: my_bookings.php?msg=Invalid+payment+data'); exit; }
// Validate booking and payment method
$res = $mysqli->query("SELECT b.*, u.email, u.first_name, u.last_name, r.room_number, h.hotel_name, pm.method_name FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id JOIN payment_methods pm ON pm.payment_method_id=$payment_method_id WHERE b.booking_id=$booking_id AND b.user_id=$user_id AND b.payment_status='pending' LIMIT 1");
$row = $res->fetch_assoc();
if (!$row) { header('Location: my_bookings.php?msg=Invalid+or+already+paid'); exit; }
// Mark as paid
$mysqli->query("UPDATE bookings SET payment_status='paid' WHERE booking_id=$booking_id");
// Send email receipt
$mail = new PHPMailer(true);
try {
    $mail->isSMTP();
    $mail->Host = 'smtp.example.com'; // TODO: Change to your SMTP server
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // TODO: Change to your SMTP username
    $mail->Password = 'yourpassword';   // TODO: Change to your SMTP password
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    $mail->setFrom('<EMAIL>', 'SmartHotel');
    $mail->addAddress($row['email'], $row['first_name'].' '.$row['last_name']);
    $mail->isHTML(true);
    $mail->Subject = 'Payment Receipt - Booking #'.$booking_id;
    $mail->Body = '<b>Dear '.htmlspecialchars($row['first_name']).',</b><br><br>'
        .'Thank you for your payment. Here is your booking receipt:<br><br>'
        .'<b>Hotel:</b> '.htmlspecialchars($row['hotel_name']).'<br>'
        .'<b>Room:</b> '.htmlspecialchars($row['room_number']).'<br>'
        .'<b>Check-in:</b> '.$row['check_in_date'].'<br>'
        .'<b>Check-out:</b> '.$row['check_out_date'].'<br>'
        .'<b>Total Paid:</b> ₱'.number_format($row['total_price'],2).'<br>'
        .'<b>Payment Method:</b> '.htmlspecialchars($row['method_name']).'<br><br>'
        .'We look forward to welcoming you!<br><br>SmartHotel Team';
    $mail->send();
    header('Location: my_bookings.php?msg=Payment+successful+and+receipt+sent');
    exit;
} catch (Exception $e) {
    header('Location: my_bookings.php?msg=Payment+successful+but+email+failed');
    exit;
}
