<?php
// User Payment Processing and Email Receipt (PHPMailer)
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';
session_start();
$user_id = $_SESSION['user_id'] ?? 0;
$booking_id = (int)($_POST['booking_id'] ?? 0);
$payment_method_id = (int)($_POST['payment_method_id'] ?? 0);
if (!$user_id || !$booking_id || !$payment_method_id) { header('Location: my_bookings.php?msg=Invalid+payment+data'); exit; }
// Validate booking and payment method
$res = $mysqli->query("SELECT b.*, u.email, u.first_name, u.last_name, r.room_number, h.hotel_name, pm.method_name FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id JOIN payment_methods pm ON pm.payment_method_id=$payment_method_id WHERE b.booking_id=$booking_id AND b.user_id=$user_id AND b.payment_status='pending' LIMIT 1");
$row = $res->fetch_assoc();
if (!$row) { header('Location: my_bookings.php?msg=Invalid+or+already+paid'); exit; }
// Mark as paid
$mysqli->query("UPDATE bookings SET payment_status='paid' WHERE booking_id=$booking_id");
// Send email receipt
$mail = new PHPMailer(true);
try {
    $mail->isSMTP();
    $mail->Host = 'smtp.example.com'; // TODO: Change to your SMTP server
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // TODO: Change to your SMTP username
    $mail->Password = 'yourpassword';   // TODO: Change to your SMTP password
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    $mail->setFrom('<EMAIL>', 'SmartHotel');
    $mail->addAddress($row['email'], $row['first_name'].' '.$row['last_name']);
    $mail->isHTML(true);
    $mail->Subject = 'Payment Receipt - Booking #' . $booking_id;
    $mail->Body = '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">'
        .'<h2 style="color: #4e73df; text-align: center;">Payment Receipt</h2>'
        .'<p>Dear ' . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . ',</p>'
        .'<p>Thank you for your payment. Here is your booking receipt:</p>'
        .'<div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin: 20px 0;">'
        .'<h3 style="margin-top: 0; color: #4e73df;">Booking Details</h3>'
        .'<p><strong>Booking ID:</strong> ' . htmlspecialchars($booking_id) . '</p>'
        .'<p><strong>Hotel:</strong> ' . htmlspecialchars($row['hotel_name']) . '</p>'
        .'<p><strong>Room:</strong> ' . htmlspecialchars($row['room_number']) . '</p>'
        .'<p><strong>Check-in:</strong> ' . htmlspecialchars($row['check_in_date']) . '</p>'
        .'<p><strong>Check-out:</strong> ' . htmlspecialchars($row['check_out_date']) . '</p>'
        .'<p><strong>Total Paid:</strong> ₱' . number_format($row['total_price'],2) . '</p>'
        .'<p><strong>Payment Method:</strong> ' . htmlspecialchars($row['method_name']) . '</p>'
        .'</div>'
        .'<p>If you have any questions or need to make changes to your reservation, please contact us.</p>'
        .'<p>We look forward to welcoming you!</p>'
        .'<p style="text-align: center; margin-top: 30px; color: #858796; font-size: 14px;">'
        . '&copy; ' . date('Y') . ' SmartHotel Booking System' . '</p>'
        .'</div>';
    $mail->send();
    header('Location: receipt.php?id=' . $booking_id . '&success=1');
    exit;
} catch (Exception $e) {
    header('Location: receipt.php?id=' . $booking_id . '&success=0');
    exit;
}
