<?php
// Contact Page
include 'includes/header.php';
include 'includes/db.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow border-0 rounded-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-envelope"></i> Contact Us</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($_GET['msg']) && $_GET['msg'] == 'success'): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Your message has been sent successfully! We will get back to you soon.
                        </div>
                    <?php elseif (isset($_GET['msg']) && $_GET['msg'] == 'error'): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> There was an error sending your message. Please try again later.
                        </div>
                    <?php endif; ?>
                    
                    <p class="lead mb-4">Have questions or need assistance? Fill out the form below and our team will get back to you as soon as possible.</p>
                    
                    <form action="contact_process.php" method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Your Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-5">
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-primary text-white mx-auto mb-4">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4>Our Location</h4>
                    <p class="mb-0">123 Main Street<br>Cabadbaran City<br>Agusan del Norte, Philippines</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-primary text-white mx-auto mb-4">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4>Phone Number</h4>
                    <p class="mb-0">+63 (*************<br>+63 (*************</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-primary text-white mx-auto mb-4">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4>Email Address</h4>
                    <p class="mb-0"><EMAIL><br><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.card {
    transition: transform 0.3s;
    border-radius: 10px;
}

.card:hover {
    transform: translateY(-5px);
}

.alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.form-control {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}
</style>

<?php include 'includes/footer.php'; ?>
