<?php
session_start();
// User Activity Log Page
include 'includes/header.php';
include 'includes/db.php';
$user_id = $_SESSION['user_id'] ?? 0;
if (!$user_id) { header('Location: login.php?msg=Please+login+first'); exit; }
$res = $mysqli->query("SELECT * FROM user_activity_logs WHERE user_id=$user_id ORDER BY timestamp DESC LIMIT 100");
?>
<div class="container" style="margin-top:2rem;max-width:900px;">
    <h2 class="mb-4" style="font-weight:600;">My Activity Log</h2>
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Action</th>
                    <th>Description</th>
                    <th>IP Address</th>
                    <th>User Agent</th>
                    <th>Date/Time</th>
                </tr>
            </thead>
            <tbody>
            <?php while($row = $res->fetch_assoc()): ?>
                <tr>
                    <td><?=htmlspecialchars($row['action'])?></td>
                    <td><?=htmlspecialchars($row['description'])?></td>
                    <td><?=htmlspecialchars($row['ip_address'])?></td>
                    <td><?=htmlspecialchars($row['user_agent'])?></td>
                    <td><?=htmlspecialchars($row['timestamp'])?></td>
                </tr>
            <?php endwhile; ?>
            <?php if ($res->num_rows == 0): ?>
                <tr><td colspan="5" class="text-center text-muted">No activity found.</td></tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
