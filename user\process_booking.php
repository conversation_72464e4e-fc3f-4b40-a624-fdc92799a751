<?php
// User Booking Processing
include 'includes/db.php';
session_start();
$user_id = $_SESSION['user_id'] ?? 0;
if (!$user_id) { header('Location: login.php?msg=Please+login+first'); exit; }
$room_id = (int)($_POST['room_id'] ?? 0);
$check_in = $_POST['check_in'] ?? '';
$check_out = $_POST['check_out'] ?? '';
$special_requests = $_POST['special_requests'] ?? '';
if (!$room_id || !$check_in || !$check_out) { header('Location: hotels.php?msg=Invalid+booking+data'); exit; }
// Get room price
$res = $mysqli->query("SELECT price FROM rooms WHERE room_id=$room_id");
$room = $res->fetch_assoc();
if (!$room) { header('Location: hotels.php?msg=Room+not+found'); exit; }
$total_price = $room['price'];
// Insert booking
$stmt = $mysqli->prepare("INSERT INTO bookings (user_id, room_id, check_in_date, check_out_date, total_price, booking_status, payment_status, special_requests) VALUES (?, ?, ?, ?, ?, 'pending', 'pending', ?)");
$stmt->bind_param('iissds', $user_id, $room_id, $check_in, $check_out, $total_price, $special_requests);
$stmt->execute();
$booking_id = $stmt->insert_id;
$stmt->close();
// Save selected amenities if any
if (!empty($_POST['amenities']) && is_array($_POST['amenities'])) {
    $amenities = $_POST['amenities'];
    $values = [];
    foreach ($amenities as $amenity_id) {
        $amenity_id = (int)$amenity_id;
        $values[] = "($booking_id, $amenity_id)";
    }
    if ($values) {
        $sql = "INSERT INTO booking_amenities (booking_id, amenity_id) VALUES " . implode(',', $values);
        $mysqli->query($sql);
    }
}
// Log user activity
$ip = $_SERVER['REMOTE_ADDR'] ?? '';
$ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
$mysqli->query("INSERT INTO user_activity_logs (user_id, action, description, ip_address, user_agent) VALUES ($user_id, 'booking', 'Booked room $room_id from $check_in to $check_out', '$ip', '$ua')");
header('Location: my_bookings.php?msg=Booking+submitted+successfully');
exit;
