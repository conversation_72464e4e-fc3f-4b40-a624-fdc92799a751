<?php
// User Booking Processing
include 'includes/db.php';
include 'includes/amenity_pricing.php';
session_start();
$user_id = $_SESSION['user_id'] ?? 0;
if (!$user_id) { header('Location: login.php?msg=Please+login+first'); exit; }
$room_id = (int)($_POST['room_id'] ?? 0);
$check_in = $_POST['check_in'] ?? '';
$check_out = $_POST['check_out'] ?? '';
$special_requests = $_POST['special_requests'] ?? '';
if (!$room_id || !$check_in || !$check_out) { header('Location: hotels.php?msg=Invalid+booking+data'); exit; }
// Get room price
$res = $mysqli->query("SELECT price FROM rooms WHERE room_id=$room_id");
$room = $res->fetch_assoc();
if (!$room) { header('Location: hotels.php?msg=Room+not+found'); exit; }
// Calculate total price (room + amenities + extra amenities)
$total_price = floatval($room['price']);

if (!empty($_POST['amenities']) && is_array($_POST['amenities'])) {
    $amenity_ids = array_map('intval', $_POST['amenities']);
    $total_price += calculateAmenitiesTotal($amenity_ids);
}
if (!empty($_POST['extra_amenities']) && is_array($_POST['extra_amenities'])) {
    foreach ($_POST['extra_amenities'] as $extra) {
        list($id, $name, $price) = explode('|', $extra);
        $total_price += floatval($price);
    }
}
// Insert booking
$stmt = $mysqli->prepare("INSERT INTO bookings (user_id, room_id, check_in_date, check_out_date, total_price, booking_status, payment_status, special_requests) VALUES (?, ?, ?, ?, ?, 'pending', 'pending', ?)");
$stmt->bind_param('iissds', $user_id, $room_id, $check_in, $check_out, $total_price, $special_requests);
$stmt->execute();
$booking_id = $stmt->insert_id;
$stmt->close();
// Save selected amenities if any
if (!empty($_POST['amenities']) && is_array($_POST['amenities'])) {
    $amenities = $_POST['amenities'];
    $values = [];
    foreach ($amenities as $amenity_id) {
        $amenity_id = (int)$amenity_id;
        $values[] = "($booking_id, $amenity_id)";
    }
    if ($values) {
        $sql = "INSERT INTO booking_amenities (booking_id, amenity_id) VALUES " . implode(',', $values);
        $mysqli->query($sql);
    }
}
// Save extra amenities (custom or add-on)
if (!empty($_POST['extra_amenities']) && is_array($_POST['extra_amenities'])) {
    foreach ($_POST['extra_amenities'] as $extra) {
        // Format: id|name|price
        list($id, $name, $price) = explode('|', $extra);
        $name = $mysqli->real_escape_string($name);
        $price = floatval($price);
        $mysqli->query("INSERT INTO booking_amenities (booking_id, amenity_id, custom_name, custom_price) VALUES ($booking_id, 0, '$name', $price)");
    }
}
// Log user activity
$ip = $_SERVER['REMOTE_ADDR'] ?? '';
$ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
$mysqli->query("INSERT INTO user_activity_logs (user_id, action, description, ip_address, user_agent) VALUES ($user_id, 'booking', 'Booked room $room_id from $check_in to $check_out', '$ip', '$ua')");

// Send booking confirmation email
include_once __DIR__ . '/../includes/mailer.php';

// Fetch user details
$user_res = $mysqli->query("SELECT first_name, last_name, email FROM users WHERE user_id=$user_id LIMIT 1");
$user = $user_res ? $user_res->fetch_assoc() : null;
// Fetch room, hotel, and type details
$room_res = $mysqli->query("SELECT r.*, h.hotel_name, rt.type_name FROM rooms r JOIN hotels h ON r.hotel_id=h.hotel_id JOIN room_types rt ON r.room_type_id=rt.room_type_id WHERE r.room_id=$room_id LIMIT 1");
$room_info = $room_res ? $room_res->fetch_assoc() : null;

if ($user && $room_info) {
    // Count guests (if you have this in booking form, otherwise default)
    $adults = isset($_POST['adults']) ? intval($_POST['adults']) : 1;
    $children = isset($_POST['children']) ? intval($_POST['children']) : 0;
    // Calculate total amount (room price + amenities + extra amenities)
    $total_amount = floatval($room_info['price']);
    if (!empty($_POST['amenities']) && is_array($_POST['amenities'])) {
        $amenity_ids = array_map('intval', $_POST['amenities']);
        $total_amount += calculateAmenitiesTotal($amenity_ids);
    }
    if (!empty($_POST['extra_amenities']) && is_array($_POST['extra_amenities'])) {
        foreach ($_POST['extra_amenities'] as $extra) {
            list($id, $name, $price) = explode('|', $extra);
            $total_amount += floatval($price);
        }
    }
    $booking = [
        'booking_id' => $booking_id,
        'first_name' => $user['first_name'],
        'last_name' => $user['last_name'],
        'hotel_name' => $room_info['hotel_name'],
        'room_type' => $room_info['type_name'],
        'check_in' => $check_in,
        'check_out' => $check_out,
        'adults' => $adults,
        'children' => $children,
        'total_amount' => $total_amount
    ];
    // Send email
    sendBookingConfirmationEmail($mysqli, $user['email'], $booking);
}

header('Location: payment.php?id=' . $booking_id);
exit;
