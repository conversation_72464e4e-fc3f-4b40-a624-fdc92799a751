<?php
// Modern Admin Dashboard with Graph, Date Range Filter, and Notifications
// Functionality: Shows key stats, graph for most visited hotels with date range filter, and notifications for booking approvals
// Design: Modern, responsive, visually consistent
// Code Neatness: Well-structured, meaningful comments
include 'includes/db.php';
include 'includes/header.php';

// Handle date range filter
$range = $_GET['range'] ?? 'weekly';
$start = $end = '';
if ($range === 'weekly') {
    $start = date('Y-m-d', strtotime('-6 days'));
    $end = date('Y-m-d');
} elseif ($range === 'monthly') {
    $start = date('Y-m-01');
    $end = date('Y-m-d');
} elseif ($range === 'yearly') {
    $start = date('Y-01-01');
    $end = date('Y-m-d');
} else {
    $start = date('Y-m-d', strtotime('-6 days'));
    $end = date('Y-m-d');
    $range = 'weekly';
}
?>
<div class="admin-content">
    <div class="page-header">
        <h2><i class="fa fa-tachometer-alt"></i> Dashboard</h2>
        <div class="breadcrumb">
            <span><i class="fa fa-home"></i> Home</span>
            <span class="separator">/</span>
            <span>Dashboard</span>
        </div>
    </div>

    <!-- Status Messages -->
    <?php if (isset($_GET['msg'])): ?>
        <?php
        $msg = $_GET['msg'];
        $alertClass = 'alert-info';
        $icon = 'fa-info-circle';

        if (strpos($msg, 'approved+and+guest+notified') !== false) {
            $alertClass = 'alert-success';
            $icon = 'fa-check-circle';
            $msg = 'Booking approved successfully and guest has been notified via email!';
        } elseif (strpos($msg, 'approved+but+email+failed') !== false) {
            $alertClass = 'alert-warning';
            $icon = 'fa-exclamation-triangle';
            $msg = 'Booking approved successfully, but email notification failed.';
            if (isset($_GET['error'])) {
                $msg .= ' Error: ' . htmlspecialchars(urldecode($_GET['error']));
            }
        } elseif (strpos($msg, 'approved+but+email+not+configured') !== false) {
            $alertClass = 'alert-warning';
            $icon = 'fa-exclamation-triangle';
            $msg = 'Booking approved successfully, but email is not configured. Please configure email settings to send notifications.';
        } elseif (strpos($msg, 'Invalid+booking') !== false) {
            $alertClass = 'alert-danger';
            $icon = 'fa-times-circle';
            $msg = 'Invalid booking ID or booking not found.';
        } else {
            $msg = htmlspecialchars(str_replace('+', ' ', $msg));
        }
        ?>
        <div class="alert <?= $alertClass ?> alert-dismissible fade show" role="alert">
            <i class="fa <?= $icon ?> me-2"></i>
            <?= $msg ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            <?php if ($alertClass === 'alert-warning' && strpos($_GET['msg'], 'email') !== false): ?>
                <hr>
                <p class="mb-0">
                    <a href="email_settings.php" class="alert-link">Configure Email Settings</a> to enable email notifications.
                </p>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Stats Cards Row -->
    <div class="stats-row">
        <?php
        $users = $mysqli->query("SELECT COUNT(*) FROM users")->fetch_row()[0];
        $rooms = $mysqli->query("SELECT COUNT(*) FROM rooms")->fetch_row()[0];
        $bookings = $mysqli->query("SELECT COUNT(*) FROM bookings")->fetch_row()[0];
        $revenue = $mysqli->query("SELECT IFNULL(SUM(total_price),0) FROM bookings WHERE payment_status='paid'")->fetch_row()[0];
        ?>
        <div class="stats-card stats-card-primary">
            <div class="stats-card-icon">
                <i class="fa fa-users"></i>
            </div>
            <div class="stats-card-content">
                <h3><?= $users ?></h3>
                <p>Total Users</p>
            </div>
        </div>

        <div class="stats-card stats-card-success">
            <div class="stats-card-icon">
                <i class="fa fa-bed"></i>
            </div>
            <div class="stats-card-content">
                <h3><?= $rooms ?></h3>
                <p>Available Rooms</p>
            </div>
        </div>

        <div class="stats-card stats-card-warning">
            <div class="stats-card-icon">
                <i class="fa fa-calendar-check"></i>
            </div>
            <div class="stats-card-content">
                <h3><?= $bookings ?></h3>
                <p>Total Bookings</p>
            </div>
        </div>

        <div class="stats-card stats-card-danger">
            <div class="stats-card-icon">
                <i class="fa fa-coins"></i>
            </div>
            <div class="stats-card-content">
                <h3>₱<?= number_format($revenue,2) ?></h3>
                <p>Total Revenue</p>
            </div>
        </div>
    </div>

    <div class="dashboard-row">
        <!-- Notifications for booking approvals -->
        <div class="card notifications-card">
            <h4><i class="fa fa-bell"></i> Pending Booking Approvals</h4>
            <div class="notification-list">
            <?php
            $pending = $mysqli->query("SELECT b.booking_id, u.first_name, u.last_name, r.room_number, h.hotel_name, b.check_in_date, b.check_out_date FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.booking_status='pending' ORDER BY b.created_at DESC LIMIT 5");
            if ($pending->num_rows > 0) {
                while ($row = $pending->fetch_assoc()) {
                    echo '<div class="notification-item">';
                    echo '<div class="notification-icon"><i class="fa fa-calendar-plus"></i></div>';
                    echo '<div class="notification-content">';
                    echo '<p><strong>'.htmlspecialchars($row['first_name'].' '.$row['last_name']).'</strong> requested booking for <strong>'.htmlspecialchars($row['hotel_name']).' (Room '.$row['room_number'].')</strong></p>';
                    echo '<p class="notification-meta">From <strong>'.$row['check_in_date'].'</strong> to <strong>'.$row['check_out_date'].'</strong></p>';
                    echo '</div>';
                    echo '<div class="notification-actions">';
                    echo '<a href="approve_booking.php?id='.$row['booking_id'].'" class="btn btn-sm btn-success"><i class="fa fa-check"></i> Approve</a>';
                    echo '<a href="reject_booking.php?id='.$row['booking_id'].'" class="btn btn-sm btn-danger"><i class="fa fa-times"></i> Reject</a>';
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                echo '<div class="empty-state">';
                echo '<i class="fa fa-check-circle"></i>';
                echo '<p>No pending bookings for approval.</p>';
                echo '</div>';
            }
            ?>
            </div>
        </div>

        <!-- Graph for most visited hotels based on most visited room with date range filter -->
        <div class="card chart-card">
            <div class="card-header">
                <h4><i class="fa fa-chart-bar"></i> Most Visited Hotels</h4>
                <form method="get" class="chart-filter">
                    <label for="range">Time Range:</label>
                    <select name="range" id="range" onchange="this.form.submit()">
                        <option value="weekly"<?= $range==='weekly'?' selected':'' ?>>Weekly</option>
                        <option value="monthly"<?= $range==='monthly'?' selected':'' ?>>Monthly</option>
                        <option value="yearly"<?= $range==='yearly'?' selected':'' ?>>Yearly</option>
                    </select>
                </form>
            </div>
            <div class="chart-container">
                <canvas id="hotelVisitsChart"></canvas>
            </div>
            <?php
            // Get most visited hotels based on user_activity_logs (page_view for rooms) in date range
            $hotelVisits = $mysqli->prepare("SELECT h.hotel_name, COUNT(*) as visits FROM user_activity_logs l JOIN rooms r ON l.description LIKE CONCAT('%Room ', r.room_number, '%') JOIN hotels h ON r.hotel_id = h.hotel_id WHERE l.action='page_view' AND l.description LIKE '%rooms page%' AND DATE(l.timestamp) BETWEEN ? AND ? GROUP BY h.hotel_id ORDER BY visits DESC LIMIT 5");
            $hotelVisits->bind_param('ss', $start, $end);
            $hotelVisits->execute();
            $result = $hotelVisits->get_result();
            $hotelNames = [];
            $hotelCounts = [];
            while ($row = $result->fetch_assoc()) {
                $hotelNames[] = $row['hotel_name'];
                $hotelCounts[] = $row['visits'];
            }
            ?>
            <script>
            document.addEventListener('DOMContentLoaded', function() {
                const ctx = document.getElementById('hotelVisitsChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: <?= json_encode($hotelNames) ?>,
                        datasets: [{
                            label: 'Visits',
                            data: <?= json_encode($hotelCounts) ?>,
                            backgroundColor: [
                                'rgba(78, 115, 223, 0.7)',
                                'rgba(28, 200, 138, 0.7)',
                                'rgba(246, 194, 62, 0.7)',
                                'rgba(231, 74, 59, 0.7)',
                                'rgba(54, 185, 204, 0.7)'
                            ],
                            borderColor: [
                                'rgb(78, 115, 223)',
                                'rgb(28, 200, 138)',
                                'rgb(246, 194, 62)',
                                'rgb(231, 74, 59)',
                                'rgb(54, 185, 204)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            tooltip: {
                                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                titleColor: '#5a5c69',
                                bodyColor: '#5a5c69',
                                borderColor: '#e3e6f0',
                                borderWidth: 1,
                                padding: 15,
                                displayColors: false,
                                callbacks: {
                                    label: function(context) {
                                        return context.parsed.y + ' visits';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    precision: 0
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            });

            // Handle alert dismissal
            document.querySelectorAll('.btn-close').forEach(button => {
                button.addEventListener('click', function() {
                    const alert = this.closest('.alert');
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 150);
                });
            });
            </script>
            <div class="chart-footer">
                <p>Showing data from <strong><?= htmlspecialchars($start) ?></strong> to <strong><?= htmlspecialchars($end) ?></strong> (<?= ucfirst($range) ?>)</p>
            </div>
        </div>
    </div>

    <!-- Recent Bookings Row -->
    <div class="card recent-bookings">
        <div class="card-header">
            <h4><i class="fa fa-calendar"></i> Recent Bookings</h4>
            <a href="bookings.php" class="btn btn-sm btn-primary">View All</a>
        </div>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Guest</th>
                        <th>Room</th>
                        <th>Check-in</th>
                        <th>Check-out</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $recentBookings = $mysqli->query("SELECT b.booking_id, CONCAT(u.first_name, ' ', u.last_name) as guest, r.room_number, h.hotel_name, b.check_in_date, b.check_out_date, b.booking_status FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id ORDER BY b.created_at DESC LIMIT 5");

                    if ($recentBookings->num_rows > 0) {
                        while ($booking = $recentBookings->fetch_assoc()) {
                            $statusClass = '';
                            switch ($booking['booking_status']) {
                                case 'confirmed': $statusClass = 'success'; break;
                                case 'pending': $statusClass = 'warning'; break;
                                case 'cancelled': $statusClass = 'danger'; break;
                                default: $statusClass = 'secondary';
                            }

                            echo '<tr>';
                            echo '<td>#'.$booking['booking_id'].'</td>';
                            echo '<td>'.htmlspecialchars($booking['guest']).'</td>';
                            echo '<td>'.htmlspecialchars($booking['hotel_name']).' - Room '.$booking['room_number'].'</td>';
                            echo '<td>'.$booking['check_in_date'].'</td>';
                            echo '<td>'.$booking['check_out_date'].'</td>';
                            echo '<td><span class="status-badge status-'.$statusClass.'">'.ucfirst($booking['booking_status']).'</span></td>';
                            echo '<td>';
                            echo '<a href="view_booking.php?id='.$booking['booking_id'].'" class="btn btn-sm btn-primary"><i class="fa fa-eye"></i></a>';
                            echo '</td>';
                            echo '</tr>';
                        }
                    } else {
                        echo '<tr><td colspan="7" class="text-center">No recent bookings found</td></tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* Dashboard Specific Styles */

/* Alert Styles */
.alert {
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    position: relative;
    display: flex;
    align-items: flex-start;
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

.alert-dismissible {
    padding-right: 3rem;
}

.btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem;
    background: none;
    border: 0;
    font-size: 1.125rem;
    opacity: 0.5;
    cursor: pointer;
}

.btn-close:hover {
    opacity: 0.75;
}

.alert-link {
    font-weight: 700;
    text-decoration: underline;
}

.me-2 {
    margin-right: 0.5rem;
}

.mb-0 {
    margin-bottom: 0;
}

.fade {
    transition: opacity 0.15s linear;
}

.show {
    opacity: 1;
}
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #858796;
}

.breadcrumb span {
    display: flex;
    align-items: center;
}

.breadcrumb i {
    margin-right: 5px;
}

.separator {
    margin: 0 10px;
    color: #d1d3e2;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.dashboard-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.notifications-card, .chart-card {
    height: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e3e6f0;
    margin-bottom: 1rem;
}

.chart-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-filter label {
    margin: 0;
    font-size: 0.85rem;
}

.chart-filter select {
    padding: 0.3rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    margin: 0;
    min-width: 120px;
}

.chart-container {
    height: 300px;
    position: relative;
}

.chart-footer {
    margin-top: 1rem;
    font-size: 0.85rem;
    color: #858796;
    text-align: center;
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(78, 115, 223, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-content p {
    margin: 0;
    line-height: 1.5;
}

.notification-meta {
    font-size: 0.85rem;
    color: #858796;
    margin-top: 0.3rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #858796;
}

.empty-state i {
    font-size: 3rem;
    color: #e3e6f0;
    margin-bottom: 1rem;
}

.table-responsive {
    overflow-x: auto;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
}

.status-success {
    background-color: rgba(28, 200, 138, 0.1);
    color: var(--secondary-color);
}

.status-warning {
    background-color: rgba(246, 194, 62, 0.1);
    color: var(--accent-color);
}

.status-danger {
    background-color: rgba(231, 74, 59, 0.1);
    color: var(--danger-color);
}

.status-secondary {
    background-color: rgba(90, 92, 105, 0.1);
    color: #5a5c69;
}

@media (max-width: 992px) {
    .dashboard-row {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
