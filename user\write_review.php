<?php
// Write a Review Page
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include 'includes/header.php';
include 'includes/db.php';
$user_id = $_SESSION['user_id'] ?? 0;
if (!$user_id) { header('Location: login.php?msg=Please+login+first'); exit; }
// Only allow review for completed bookings
$res = $mysqli->query("SELECT b.booking_id, h.hotel_name FROM bookings b JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.user_id=$user_id AND b.booking_status='completed' AND b.booking_id NOT IN (SELECT booking_id FROM reviews) ORDER BY b.check_out_date DESC");
?>
<div class="container" style="margin-top:2rem;max-width:600px;">
    <h2 class="mb-4" style="font-weight:600;">Write a Review</h2>
    <form method="post" action="write_review_process.php">
        <div class="mb-3">
            <label class="form-label">Booking</label>
            <select class="form-select" name="booking_id" required>
                <option value="">Select Booking</option>
                <?php while($row = $res->fetch_assoc()): ?>
                <option value="<?=$row['booking_id']?>">Hotel: <?=htmlspecialchars($row['hotel_name'])?> (Booking #<?=$row['booking_id']?>)</option>
                <?php endwhile; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Rating</label>
            <select class="form-select" name="rating" required>
                <option value="">Select Rating</option>
                <option value="5">5 - Excellent</option>
                <option value="4">4 - Very Good</option>
                <option value="3">3 - Good</option>
                <option value="2">2 - Fair</option>
                <option value="1">1 - Poor</option>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Comment</label>
            <textarea class="form-control" name="comment" rows="3" required></textarea>
        </div>
        <button class="btn btn-success w-100" type="submit">Submit Review</button>
    </form>
    <!-- Payment Methods Section -->
    <div class="mt-5">
        <h3 class="mb-3" style="font-weight:600;">Payment Methods</h3>
        <div class="card mb-3">
            <div class="card-body d-flex align-items-center">
                <i class="fas fa-credit-card fa-2x text-primary me-3"></i>
                <div>
                    <strong>Credit/Debit Card</strong><br>
                    Pay securely using your Visa, MasterCard, or JCB card.
                </div>
            </div>
        </div>
        <div class="card mb-3">
            <div class="card-body d-flex align-items-center">
                <i class="fas fa-university fa-2x text-success me-3"></i>
                <div>
                    <strong>Bank Transfer</strong><br>
                    Transfer payment to our official bank account. Details will be sent after booking.
                </div>
            </div>
        </div>
        <div class="card mb-3">
            <div class="card-body d-flex align-items-center">
                <i class="fas fa-wallet fa-2x text-warning me-3"></i>
                <div>
                    <strong>GCash/PayMaya</strong><br>
                    Pay via GCash or PayMaya. QR code and instructions will be provided after booking.
                </div>
            </div>
        </div>
        <div class="card mb-3">
            <div class="card-body d-flex align-items-center">
                <i class="fas fa-money-bill-wave fa-2x text-secondary me-3"></i>
                <div>
                    <strong>Cash on Arrival</strong><br>
                    Pay at the hotel front desk upon check-in.
                </div>
            </div>
        </div>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
