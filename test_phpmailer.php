<?php
// Include the autoloader
require_once 'autoload.php';

// Include PHPMailer
require_once 'PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once 'PHPMailer-PHPMailer-19debc7/src/SMTP.php';
require_once 'PHPMailer-PHPMailer-19debc7/src/Exception.php';

// Create a logger instance
$logger = new Psr\Log\SimpleLogger();

// Create a new PHPMailer instance
$mail = new PHPMailer\PHPMailer\PHPMailer(true);

// Set the logger
$mail->Debugoutput = $logger;

// Enable debug output
$mail->SMTPDebug = 2;

// Test the logger
$mail->edebug('Testing logger');

echo "PHPMailer test completed successfully!\n";
