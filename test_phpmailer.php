<?php
// Standalone PHPMailer test for debugging mail issues interactively
require_once __DIR__ . '/includes/mailer.php';
require_once __DIR__ . '/user/includes/db.php';
require_once __DIR__ . '/PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/PHPMailer-PHPMailer-19debc7/src/SMTP.php';
require_once __DIR__ . '/PHPMailer-PHPMailer-19debc7/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

$to = $_POST['to'] ?? '';
$sent = false;
$error = '';
$debug = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && filter_var($to, FILTER_VALIDATE_EMAIL)) {
    $subject = 'PHPMailer Test - SmartHotel';
    $body = '<h2>PHPMailer Test</h2><p>This is a test email sent from PHPMailer on ' . date('Y-m-d H:i:s') . '.</p>';
    $settings = getEmailSettings($mysqli);
    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->Port = $settings['smtp_port'];
        $mail->SMTPAuth = $settings['smtp_auth'] === '1';
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        $mail->setFrom($settings['from_email'], $settings['from_name']);
        $mail->addAddress($to);
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        $mail->AltBody = strip_tags($body);
        if ($settings['smtp_secure'] === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($settings['smtp_secure'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = '';
            $mail->SMTPAutoTLS = false;
        }
        // Enable SMTP debug output
        $mail->SMTPDebug = SMTP::DEBUG_SERVER;
        $mail->Debugoutput = function($str, $level) use (&$debug) {
            $debug .= "<pre style='color:#333;background:#f8f9fa;padding:8px;border-radius:4px;margin:0;'>" . htmlspecialchars($str) . "</pre>\n";
        };
        $mail->send();
        $sent = true;
    } catch (Exception $e) {
        $error = $mail->ErrorInfo;
        $debug .= '<pre>' . htmlspecialchars($e) . '</pre>';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>PHPMailer Test - SmartHotel</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body { background: #f4f6fb; font-family: Arial, sans-serif; }
        .test-mail-card { max-width: 500px; margin: 5vh auto; border-radius: 1.5rem; box-shadow: 0 8px 32px rgba(30,40,90,0.12); background: #fff; padding: 2.5rem 2rem 2rem 2rem; }
        .brand { font-size: 2rem; font-weight: 800; color: #4e73df; letter-spacing: 1px; }
        .brand i { color: #224abe; }
        .debug-output { max-height: 300px; overflow:auto; background:#f8f9fa; border-radius:6px; margin-top:1rem; }
    </style>
</head>
<body>
<div class="test-mail-card">
    <div class="text-center mb-4">
        <span class="brand"><i class="fa fa-hotel me-2"></i>SmartHotel</span>
        <div class="mt-2 mb-1 text-muted" style="font-size:1.1rem;">PHPMailer Test Utility</div>
    </div>
    <form method="post" class="mb-3">
        <div class="mb-3">
            <label for="to" class="form-label">Recipient Email</label>
            <input type="email" class="form-control" id="to" name="to" placeholder="Enter your email address" required value="<?=htmlspecialchars($to)?>">
        </div>
        <button class="btn btn-primary w-100" type="submit">Send Test Email</button>
    </form>
    <?php if ($sent): ?>
        <div class="alert alert-success">Test email sent successfully to <b><?=htmlspecialchars($to)?></b>.</div>
    <?php elseif ($error): ?>
        <div class="alert alert-danger">Failed to send test email: <?=htmlspecialchars($error)?></div>
    <?php endif; ?>
    <?php if ($debug): ?>
        <div class="debug-output"><?=$debug?></div>
    <?php endif; ?>
    <div class="alert alert-info mt-4">
        <b>Instructions:</b><br>
        1. Enter your email and click Send Test Email.<br>
        2. Check your inbox and spam folder.<br>
        3. If you see an error, use the debug output to fix your SMTP settings in <b>admin/email_settings.php</b> or the <b>system_settings</b> table.<br>
        4. If you see SMTP debug output, use it to diagnose connection/authentication issues.<br>
    </div>
</div>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
