<?php
/**
 * PHPMailer Test Script
 * 
 * This script tests the PHPMailer functionality directly without using the database settings.
 * It can be used to diagnose issues with PHPMailer.
 */

// Include PHPMailer classes
require_once __DIR__ . '/PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/PHPMailer-PHPMailer-19debc7/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

// Function to send a test email
function sendTestEmail($recipient, $smtp_host, $smtp_port, $smtp_secure, $smtp_auth, $smtp_username, $smtp_password, $from_email, $from_name) {
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->Port = $smtp_port;
        
        // Enable verbose debug output
        $mail->SMTPDebug = SMTP::DEBUG_SERVER;
        
        // Set security
        if ($smtp_secure === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($smtp_secure === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = '';
            $mail->SMTPAutoTLS = false;
        }
        
        // Set authentication
        if ($smtp_auth) {
            $mail->SMTPAuth = true;
            $mail->Username = $smtp_username;
            $mail->Password = $smtp_password;
        } else {
            $mail->SMTPAuth = false;
        }
        
        // Recipients
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($recipient);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'PHPMailer Test';
        $mail->Body = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
                <h2 style="color: #4e73df; text-align: center;">PHPMailer Test</h2>
                <p>This is a test email sent from the PHPMailer test script.</p>
                <p>If you received this email, your email settings are working correctly!</p>
                <div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <p style="margin: 0;"><strong>SMTP Host:</strong> ' . htmlspecialchars($smtp_host) . '</p>
                    <p style="margin: 5px 0;"><strong>SMTP Port:</strong> ' . htmlspecialchars($smtp_port) . '</p>
                    <p style="margin: 5px 0;"><strong>SMTP Security:</strong> ' . htmlspecialchars($smtp_secure) . '</p>
                    <p style="margin: 5px 0;"><strong>From Email:</strong> ' . htmlspecialchars($from_email) . '</p>
                    <p style="margin: 5px 0;"><strong>From Name:</strong> ' . htmlspecialchars($from_name) . '</p>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #858796; font-size: 14px;">
                    Sent on: ' . date('Y-m-d H:i:s') . '
                </p>
            </div>
        ';
        $mail->AltBody = 'This is a test email sent from the PHPMailer test script. If you received this email, your email settings are working correctly!';
        
        // Send the email
        $mail->send();
        echo "Email sent successfully!<br>";
        return true;
    } catch (Exception $e) {
        echo "Email could not be sent. Error: " . $mail->ErrorInfo . "<br>";
        return false;
    }
}

// Process form submission
$result = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $recipient = $_POST['recipient'] ?? '';
    $smtp_host = $_POST['smtp_host'] ?? '';
    $smtp_port = $_POST['smtp_port'] ?? '';
    $smtp_secure = $_POST['smtp_secure'] ?? '';
    $smtp_auth = isset($_POST['smtp_auth']) ? true : false;
    $smtp_username = $_POST['smtp_username'] ?? '';
    $smtp_password = $_POST['smtp_password'] ?? '';
    $from_email = $_POST['from_email'] ?? '';
    $from_name = $_POST['from_name'] ?? '';
    
    ob_start();
    $success = sendTestEmail(
        $recipient,
        $smtp_host,
        $smtp_port,
        $smtp_secure,
        $smtp_auth,
        $smtp_username,
        $smtp_password,
        $from_email,
        $from_name
    );
    $result = ob_get_clean();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHPMailer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fc;
        }
        h1, h2 {
            color: #4e73df;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        .checkbox-label {
            font-weight: normal;
        }
        .checkbox-label input {
            width: auto;
            margin-right: 10px;
        }
        button {
            background-color: #4e73df;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2e59d9;
        }
        .result {
            background-color: #f8f9fc;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PHPMailer Test</h1>
        
        <form method="post">
            <div class="form-group">
                <label for="recipient">Recipient Email</label>
                <input type="email" id="recipient" name="recipient" required>
            </div>
            
            <div class="form-group">
                <label for="smtp_host">SMTP Host</label>
                <input type="text" id="smtp_host" name="smtp_host" value="smtp.gmail.com" required>
            </div>
            
            <div class="form-group">
                <label for="smtp_port">SMTP Port</label>
                <input type="text" id="smtp_port" name="smtp_port" value="587" required>
            </div>
            
            <div class="form-group">
                <label for="smtp_secure">SMTP Security</label>
                <select id="smtp_secure" name="smtp_secure">
                    <option value="tls">TLS</option>
                    <option value="ssl">SSL</option>
                    <option value="none">None</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="smtp_auth" name="smtp_auth" checked>
                    Require Authentication
                </label>
            </div>
            
            <div class="form-group">
                <label for="smtp_username">SMTP Username</label>
                <input type="text" id="smtp_username" name="smtp_username">
            </div>
            
            <div class="form-group">
                <label for="smtp_password">SMTP Password</label>
                <input type="password" id="smtp_password" name="smtp_password">
            </div>
            
            <div class="form-group">
                <label for="from_email">From Email</label>
                <input type="email" id="from_email" name="from_email" required>
            </div>
            
            <div class="form-group">
                <label for="from_name">From Name</label>
                <input type="text" id="from_name" name="from_name" required>
            </div>
            
            <button type="submit">Send Test Email</button>
        </form>
        
        <?php if (!empty($result)): ?>
        <div class="result">
            <h2>Test Result</h2>
            <?php echo $result; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
