-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 20, 2025 at 05:21 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `book`
--

-- --------------------------------------------------------

--
-- Table structure for table `amenities`
--

CREATE TABLE `amenities` (
  `amenity_id` int(11) NOT NULL,
  `amenity_name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `amenities`
--

INSERT INTO `amenities` (`amenity_id`, `amenity_name`, `description`, `icon`, `created_at`, `updated_at`) VALUES
(1, 'Spa Access', 'Access to spa facilities', 'spa', '2025-05-19 23:48:58', '2025-05-19 23:48:58'),
(2, 'Lunch Meal', 'One-time lunch meal with drink', 'utensils', '2025-05-19 23:48:58', '2025-05-19 23:48:58'),
(3, 'Dinner Meal', 'One-time dinner meal with dessert', 'utensils', '2025-05-19 23:48:58', '2025-05-19 23:48:58'),
(4, 'Snacks Package', 'Light snacks and beverages', 'coffee', '2025-05-19 23:48:58', '2025-05-19 23:48:58'),
(5, 'In-Room Massage', '30-minute in-room massage service', 'spa', '2025-05-19 23:48:58', '2025-05-19 23:48:58'),
(6, 'Late Checkout', 'Extend room usage until 2 PM', 'clock', '2025-05-19 23:48:58', '2025-05-19 23:48:58');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `check_in_date` date NOT NULL,
  `check_out_date` date NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','completed') DEFAULT 'pending',
  `payment_status` enum('pending','paid','refunded') DEFAULT 'pending',
  `special_requests` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `package_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Triggers `bookings`
--
DELIMITER $$
CREATE TRIGGER `booking_status_update_trigger` AFTER UPDATE ON `bookings` FOR EACH ROW BEGIN
    IF OLD.booking_status != NEW.booking_status THEN
        INSERT INTO notifications (user_id, title, message, type, related_entity_type, related_entity_id)
        VALUES (NEW.user_id, 'Booking Status Updated',
               CONCAT('Your booking #', NEW.booking_id, ' status has been updated to: ', NEW.booking_status),
               'info', 'booking', NEW.booking_id);
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Stand-in structure for view `booking_details_view`
-- (See below for the actual view)
--
CREATE TABLE `booking_details_view` (
`booking_id` int(11)
,`user_id` int(11)
,`room_id` int(11)
,`check_in_date` date
,`check_out_date` date
,`total_price` decimal(10,2)
,`booking_status` enum('pending','confirmed','cancelled','completed')
,`payment_status` enum('pending','paid','refunded')
,`special_requests` text
,`first_name` varchar(50)
,`last_name` varchar(50)
,`email` varchar(100)
,`phone` varchar(20)
,`hotel_id` int(11)
,`hotel_name` varchar(100)
,`room_number` varchar(10)
,`type_name` varchar(50)
,`nights_stayed` int(7)
,`package_id` int(11)
,`package_name` varchar(100)
);

-- --------------------------------------------------------

--
-- Table structure for table `dynamic_pricing`
--

CREATE TABLE `dynamic_pricing` (
  `pricing_id` int(11) NOT NULL,
  `room_type_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `adjustment_factor` decimal(5,2) NOT NULL DEFAULT 1.00,
  `reason` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `hotels`
--

CREATE TABLE `hotels` (
  `hotel_id` int(11) NOT NULL,
  `hotel_name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `address` text NOT NULL,
  `city` varchar(50) NOT NULL,
  `star_rating` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `category_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hotels`
--

INSERT INTO `hotels` (`hotel_id`, `hotel_name`, `description`, `address`, `city`, `star_rating`, `phone`, `email`, `image_url`, `created_at`, `updated_at`, `category_id`) VALUES
(1, 'E & G Hotel and Restaurant', 'A cozy lodge in the heart of Cabadbaran. E & G Hotel offers comfortable accommodations with a homey atmosphere, making it perfect for both business and leisure travelers. The attached restaurant serves delicious local cuisine and international favorites.', 'Purok 5, Bayan ng Cabadbaran', 'Cabadbaran City', 3, '+63 9123456789', '<EMAIL>', 'eg_hotel.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 4),
(2, 'Ruth Apartelle Suite', 'Modern serviced apartments for extended stays. Ruth Apartelle Suite provides spacious, fully-furnished apartments with kitchen facilities, making it ideal for travelers seeking a home away from home. The property offers a range of amenities to ensure a comfortable and convenient stay.', 'Poblacion 8, Cabadbaran', 'Cabadbaran City', 4, '+63 9234567890', '<EMAIL>', 'ruth_apartelle.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 2),
(3, 'Manding Loreta Resort', 'Budget-friendly accommodation for travelers. Manding Loreta Resort offers clean, comfortable rooms at affordable rates. The resort features a garden setting with outdoor seating areas where guests can relax and enjoy the peaceful surroundings.', 'National Highway, Cabadbaran', 'Cabadbaran City', 3, '+63 9345678901', '<EMAIL>', 'manding_loreta.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 3),
(4, 'Gazebo Pools and Restaurant', 'Relaxing getaway with swimming pools and dining. Gazebo Pools and Restaurant is a popular destination for both locals and tourists, featuring multiple swimming pools, a restaurant serving local and international cuisine, and comfortable accommodations for overnight stays.', 'Riverside Area, Cabadbaran', 'Cabadbaran City', 3, '+63 9456789012', '<EMAIL>', 'gazebo_pools.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 3),
(5, 'Casa Alburo Hotel and Restaurant', 'Elegant hotel with fine dining options. Casa Alburo offers upscale accommodations with tastefully decorated rooms and suites. The on-site restaurant is known for its excellent cuisine and elegant ambiance, making it a favorite for special occasions and business dinners.', 'Downtown Cabadbaran', 'Cabadbaran City', 4, '+63 9567890123', '<EMAIL>', 'casa_alburo.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 1),
(6, 'Ladolcevita Inland Resort', 'Premium inland resort with leisure facilities. Ladolcevita Inland Resort provides a tranquil retreat with lush gardens, swimming pools, and recreational areas. The resort offers various accommodation options, from standard rooms to private villas, catering to different preferences and group sizes.', 'Specific Address in Cabadbaran City', 'Cabadbaran City', 4, '+63 9123456789', '<EMAIL>', 'ladolcevita.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 3),
(7, 'J & JM Hotel and Restaurant', 'A comfortable and affordable hotel near Shell gas station and Vross store. J & JM Hotel offers standard, deluxe, and family rooms with modern amenities. The convenient location makes it an excellent choice for travelers exploring Cabadbaran City.', 'Shell Highway, near Vross', 'Cabadbaran City', 3, '+63 9353849453', '<EMAIL>', 'j_jm_hotel.jpg', '2025-05-19 23:31:55', '2025-05-19 23:31:55', 5);

-- --------------------------------------------------------

--
-- Table structure for table `hotel_categories`
--

CREATE TABLE `hotel_categories` (
  `category_id` int(11) NOT NULL,
  `category_name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hotel_categories`
--

INSERT INTO `hotel_categories` (`category_id`, `category_name`, `description`, `icon`, `created_at`, `updated_at`) VALUES
(1, 'Luxury', 'High-end hotels with premium amenities and services', 'star', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(2, 'Business', 'Hotels catering to business travelers with work facilities', 'briefcase', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(3, 'Resort', 'Leisure properties with recreational facilities', 'umbrella-beach', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(4, 'Boutique', 'Unique, stylish hotels with personalized service', 'gem', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(5, 'Budget', 'Affordable accommodations with essential amenities', 'money-bill', '2025-05-19 23:31:55', '2025-05-19 23:31:55');

-- --------------------------------------------------------

--
-- Table structure for table `hotel_experiences`
--

CREATE TABLE `hotel_experiences` (
  `experience_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `experience_name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `price` decimal(10,2) DEFAULT 0.00,
  `duration` varchar(50) DEFAULT NULL,
  `icon` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hotel_experiences`
--

INSERT INTO `hotel_experiences` (`experience_id`, `hotel_id`, `experience_name`, `description`, `price`, `duration`, `icon`, `created_at`, `updated_at`) VALUES
(1, 1, 'Cabadbaran Culinary Tour', 'Guided tour of local markets followed by a hands-on cooking class featuring traditional Agusan dishes with our head chef.', 1200.00, '4 hours', 'utensils', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(2, 1, 'Digital Detox Package', 'Surrender your digital devices and enjoy a curated experience of mindfulness activities, including guided meditation, nature walks, and analog entertainment.', 1500.00, 'Full day', 'peace', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(3, 1, 'Local Artisan Workshop', 'Learn traditional crafts from local artisans, including weaving, pottery, or woodcarving, and create your own souvenir to take home.', 800.00, '3 hours', 'hands', '2025-05-19 23:31:55', '2025-05-19 23:31:55');

-- --------------------------------------------------------

--
-- Stand-in structure for view `hotel_info_view`
-- (See below for the actual view)
--
CREATE TABLE `hotel_info_view` (
`hotel_id` int(11)
,`hotel_name` varchar(100)
,`description` text
,`address` text
,`city` varchar(50)
,`star_rating` int(11)
,`phone` varchar(20)
,`email` varchar(100)
,`image_url` varchar(255)
,`category_name` varchar(50)
,`category_icon` varchar(50)
);

-- --------------------------------------------------------

--
-- Table structure for table `hotel_innovations`
--

CREATE TABLE `hotel_innovations` (
  `innovation_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `innovation_name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `icon` varchar(50) NOT NULL,
  `category` enum('smart_room','sustainability','experience','service','technology') NOT NULL,
  `is_featured` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hotel_innovations`
--

INSERT INTO `hotel_innovations` (`innovation_id`, `hotel_id`, `innovation_name`, `description`, `icon`, `category`, `is_featured`, `created_at`, `updated_at`) VALUES
(1, 1, 'Smart Room Control System', 'Control lighting, temperature, and entertainment systems through a tablet or smartphone app. Personalize your room environment with preset modes for relaxation, work, or sleep.', 'tablet-alt', 'smart_room', 1, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(2, 1, 'Voice-Activated Assistants', 'Each room features voice-activated assistants to control room functions, provide local information, or request hotel services without lifting a finger.', 'microphone', 'technology', 0, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(3, 1, 'Digital Concierge Service', '24/7 digital concierge available through in-room tablets and the hotel app, offering personalized recommendations and instant service requests.', 'concierge-bell', 'service', 0, '2025-05-19 23:31:55', '2025-05-19 23:31:55');

-- --------------------------------------------------------

--
-- Table structure for table `hotel_sustainability`
--

CREATE TABLE `hotel_sustainability` (
  `sustainability_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `initiative_name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `impact_metric` varchar(255) DEFAULT NULL,
  `icon` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hotel_sustainability`
--

INSERT INTO `hotel_sustainability` (`sustainability_id`, `hotel_id`, `initiative_name`, `description`, `impact_metric`, `icon`, `created_at`, `updated_at`) VALUES
(1, 1, 'Energy-Efficient Lighting', 'All hotel areas use LED lighting with motion sensors in corridors and public spaces to reduce electricity consumption.', '30% reduction in lighting energy consumption', 'lightbulb', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(2, 1, 'Local Sourcing Program', 'Our restaurant sources over 60% of ingredients from local farmers and producers within a 30-mile radius, reducing carbon footprint and supporting the local economy.', '60% locally sourced ingredients', 'carrot', '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(3, 1, 'Digital Check-in/Check-out', 'Paperless check-in and check-out process, with digital receipts and confirmations sent via email to reduce paper waste.', '90% reduction in paper usage', 'tablet-alt', '2025-05-19 23:31:55', '2025-05-19 23:31:55');

-- --------------------------------------------------------

--
-- Table structure for table `inventory_items`
--

CREATE TABLE `inventory_items` (
  `item_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `category` varchar(50) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `unit` varchar(20) NOT NULL,
  `reorder_level` int(11) NOT NULL DEFAULT 10,
  `last_restocked` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `maintenance_requests`
--

CREATE TABLE `maintenance_requests` (
  `request_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `reported_by` int(11) DEFAULT NULL,
  `issue_type` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `status` enum('pending','assigned','in_progress','completed','cancelled') DEFAULT 'pending',
  `assigned_to` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `related_entity_type` varchar(50) DEFAULT NULL,
  `related_entity_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `occupancy_stats`
--

CREATE TABLE `occupancy_stats` (
  `stat_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `total_rooms` int(11) NOT NULL,
  `occupied_rooms` int(11) NOT NULL,
  `occupancy_rate` decimal(5,2) NOT NULL,
  `average_daily_rate` decimal(10,2) NOT NULL,
  `revenue_per_available_room` decimal(10,2) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `package_amenities`
--

CREATE TABLE `package_amenities` (
  `package_id` int(11) NOT NULL,
  `amenity_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `payment_method_id` int(11) NOT NULL,
  `method_name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`payment_method_id`, `method_name`, `description`, `icon`, `is_active`, `created_at`) VALUES
(1, 'Credit/Debit Card', 'Pay with your credit or debit card', 'credit-card', 1, '2025-05-20 00:37:25'),
(2, 'PayPal', 'Pay with your PayPal account', 'paypal', 1, '2025-05-20 00:37:25'),
(3, 'GCash', 'Pay with GCash', 'mobile-alt', 1, '2025-05-20 00:37:25');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `payment_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','completed','failed','refunded') DEFAULT 'pending',
  `transaction_id` varchar(255) DEFAULT NULL,
  `payment_gateway_response` text DEFAULT NULL,
  `payment_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `remember_tokens`
--

CREATE TABLE `remember_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `remember_tokens`
--

INSERT INTO `remember_tokens` (`id`, `user_id`, `token`, `expires`, `created_at`) VALUES
(1, 5, '$2y$10$oB7K6dyF6WFQ/uEiu0pfAuWhhTvKpo7zgT2dYt4NjBD6m2ion/mj.', '2025-06-19 09:19:25', '2025-05-20 01:19:25');

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `review_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `rating` int(11) NOT NULL,
  `comment` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ;

-- --------------------------------------------------------

--
-- Table structure for table `rooms`
--

CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL,
  `hotel_id` int(11) NOT NULL,
  `room_type_id` int(11) NOT NULL,
  `room_number` varchar(10) NOT NULL,
  `floor` varchar(10) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` enum('available','booked','maintenance') DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Triggers `rooms`
--
DELIMITER $$
CREATE TRIGGER `room_status_change_trigger` AFTER UPDATE ON `rooms` FOR EACH ROW BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO room_status_logs (room_id, previous_status, new_status)
        VALUES (NEW.room_id, OLD.status, NEW.status);
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `room_amenities`
--

CREATE TABLE `room_amenities` (
  `room_id` int(11) NOT NULL,
  `amenity_id` int(11) NOT NULL,
  `room_type_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `room_amenity_packages`
--

CREATE TABLE `room_amenity_packages` (
  `package_id` int(11) NOT NULL,
  `room_type_id` int(11) NOT NULL,
  `package_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price_adjustment` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `room_availability_view`
-- (See below for the actual view)
--
CREATE TABLE `room_availability_view` (
`room_id` int(11)
,`hotel_id` int(11)
,`room_type_id` int(11)
,`room_number` varchar(10)
,`status` enum('available','booked','maintenance')
,`hotel_name` varchar(100)
,`type_name` varchar(50)
,`base_price` decimal(10,2)
,`is_booked` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `room_images`
--

CREATE TABLE `room_images` (
  `image_id` int(11) NOT NULL,
  `room_type_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `image_type` enum('main','bathroom','view','other') NOT NULL DEFAULT 'other',
  `display_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `room_sensor_data`
--

CREATE TABLE `room_sensor_data` (
  `data_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `temperature` decimal(5,2) DEFAULT NULL,
  `humidity` decimal(5,2) DEFAULT NULL,
  `air_quality` int(11) DEFAULT NULL,
  `occupancy_detected` tinyint(1) DEFAULT 0,
  `energy_usage` decimal(10,2) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `room_status_logs`
--

CREATE TABLE `room_status_logs` (
  `log_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `previous_status` enum('available','booked','maintenance') NOT NULL,
  `new_status` enum('available','booked','maintenance') NOT NULL,
  `changed_by` int(11) DEFAULT NULL,
  `change_reason` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `room_types`
--

CREATE TABLE `room_types` (
  `room_type_id` int(11) NOT NULL,
  `type_name` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `base_price` decimal(10,2) NOT NULL,
  `capacity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `room_types`
--

INSERT INTO `room_types` (`room_type_id`, `type_name`, `description`, `base_price`, `capacity`, `created_at`, `updated_at`) VALUES
(1, 'Standard', 'Our modern Standard Room offers a sleek, contemporary design with all the essentials for a comfortable stay. Featuring a plush queen-size bed with premium linens, a smart TV with streaming capabilities, high-speed WiFi, and a stylish workspace. The modern bathroom includes a rainfall shower, luxury toiletries, and LED-lit mirrors.', 1500.00, 2, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(2, 'Deluxe', 'Experience luxury in our modern Deluxe Room, designed with sophisticated contemporary elements. Enjoy the spacious layout featuring a king-size bed with premium bedding, a cozy seating area with designer furniture, and floor-to-ceiling windows offering stunning views. The room includes a 55\" smart TV, Bluetooth sound system, high-speed WiFi, and a digital concierge tablet.', 2500.00, 2, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(3, 'Suite', 'Our modern Suite represents the pinnacle of contemporary luxury. This expansive accommodation features a separate living area with designer furniture, a master bedroom with a premium king-size bed, and a dining space perfect for in-room dining. Enjoy state-of-the-art amenities including a 65\" OLED smart TV, surround sound system, high-speed WiFi, automated blackout curtains, and climate control.', 3500.00, 3, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(4, 'Family', 'Our spacious Family Room is designed with modern comfort for the whole family. Featuring two queen-size beds with premium linens, a comfortable seating area, and a large smart TV with streaming services. The room includes a mini-fridge, microwave, and coffee maker. The modern bathroom has a bathtub/shower combination and child-friendly amenities.', 4000.00, 4, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(5, 'Executive', 'Our Executive Room combines modern design with business functionality. Featuring a king-size bed with premium bedding, an ergonomic workspace with high-speed internet, and a comfortable seating area. The room includes a 55\" smart TV, Bluetooth speaker, and a Nespresso coffee machine. The elegant bathroom offers a rainfall shower, luxury toiletries, and plush bathrobes.', 3000.00, 2, '2025-05-19 23:31:55', '2025-05-19 23:31:55');

-- --------------------------------------------------------

--
-- Table structure for table `smart_room_features`
--

CREATE TABLE `smart_room_features` (
  `feature_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `icon` varchar(50) NOT NULL,
  `status` enum('active','inactive','maintenance') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `smtp_host` varchar(255) NOT NULL DEFAULT 'smtp.gmail.com',
  `smtp_port` int(11) NOT NULL DEFAULT 587,
  `smtp_username` varchar(255) NOT NULL DEFAULT '',
  `smtp_password` varchar(255) NOT NULL DEFAULT '',
  `smtp_secure` varchar(10) NOT NULL DEFAULT 'tls',
  `from_email` varchar(255) NOT NULL DEFAULT '',
  `from_name` varchar(255) NOT NULL DEFAULT 'Hotel Admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `smtp_host`, `smtp_port`, `smtp_username`, `smtp_password`, `smtp_secure`, `from_email`, `from_name`) VALUES
(1, 'smtp.gmail.com', 587, '<EMAIL>', 'ggqp rnqr rnqr rnqr', 'tls', '<EMAIL>', 'Hotel Admin');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `is_admin` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `first_name`, `last_name`, `email`, `password`, `phone`, `address`, `city`, `country`, `is_admin`, `created_at`, `updated_at`) VALUES
(1, 'Johnny', 'Guzon', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, NULL, NULL, NULL, 1, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(2, 'John', 'Doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+63 ************', '123 Main St', 'Cabadbaran City', 'Philippines', 0, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(3, 'Jane', 'Smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+63 ************', '456 Oak Ave', 'Cabadbaran City', 'Philippines', 0, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(4, 'Robert', 'Johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+63 ************', '789 Pine St', 'Cabadbaran City', 'Philippines', 0, '2025-05-19 23:31:55', '2025-05-19 23:31:55'),
(5, 'klgs', 'sge', '<EMAIL>', '$2y$10$9uPbj2APrM.I7atjVJiHT.WwXbGXwqMFbLEpIV8RLyOk03aiSEqOi', '09121659658', 'comagascas', 'cabadbaran', 'Philippines', 0, '2025-05-20 01:19:05', '2025-05-20 01:19:05');

-- --------------------------------------------------------

--
-- Table structure for table `user_activity_logs`
--

-- =========================
-- User Activity Logs Table
-- =========================
-- Records all user actions for auditing and analytics
CREATE TABLE IF NOT EXISTS `user_activity_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT, -- Primary key
  `user_id` int(11) NOT NULL,               -- Foreign key to users
  `action` varchar(50) NOT NULL,            -- Action performed by the user
  `description` text DEFAULT NULL,          -- Optional details about the action
  `ip_address` varchar(50) DEFAULT NULL,    -- IP address of the user
  `user_agent` text DEFAULT NULL,           -- Browser or device info
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(), -- When the action occurred
  PRIMARY KEY (`log_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_activity_logs`
--

INSERT INTO `user_activity_logs` (`log_id`, `user_id`, `action`, `description`, `ip_address`, `user_agent`, `timestamp`) VALUES
(1, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:19:25'),
(2, 5, 'page_view', 'Viewed login page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:19:47'),
(3, 5, 'page_view', 'Viewed register page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:19:49'),
(4, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:19:50'),
(5, 5, 'page_view', 'Viewed direct_booking page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:19:52'),
(6, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:54:11'),
(7, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 01:54:13'),
(8, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:15:38'),
(9, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:21:36'),
(10, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:22:15'),
(11, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:22:29'),
(12, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:23:56'),
(13, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:30:36'),
(14, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:30:41'),
(15, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:30:57'),
(16, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:33:00'),
(17, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:34:41'),
(18, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:36:28'),
(19, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:38:39'),
(20, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:39:06'),
(21, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:39:28'),
(22, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:58:53'),
(23, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 02:59:40'),
(24, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:01:13'),
(25, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:01:17'),
(26, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:03:06'),
(27, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:03:10'),
(28, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:04:27'),
(29, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:16:33'),
(30, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:16:36'),
(31, 5, 'page_view', 'Viewed hotel_room_booking page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:17:31'),
(32, 5, 'page_view', 'Viewed hotels page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:17:35'),
(33, 5, 'page_view', 'Viewed rooms page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:17:39'),
(34, 5, 'page_view', 'Viewed home page', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-20 03:17:48');

-- --------------------------------------------------------

--
-- Structure for view `booking_details_view`
--
DROP TABLE IF EXISTS `booking_details_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `booking_details_view`  AS SELECT `b`.`booking_id` AS `booking_id`, `b`.`user_id` AS `user_id`, `b`.`room_id` AS `room_id`, `b`.`check_in_date` AS `check_in_date`, `b`.`check_out_date` AS `check_out_date`, `b`.`total_price` AS `total_price`, `b`.`booking_status` AS `booking_status`, `b`.`payment_status` AS `payment_status`, `b`.`special_requests` AS `special_requests`, `u`.`first_name` AS `first_name`, `u`.`last_name` AS `last_name`, `u`.`email` AS `email`, `u`.`phone` AS `phone`, `h`.`hotel_id` AS `hotel_id`, `h`.`hotel_name` AS `hotel_name`, `r`.`room_number` AS `room_number`, `rt`.`type_name` AS `type_name`, to_days(`b`.`check_out_date`) - to_days(`b`.`check_in_date`) AS `nights_stayed`, `rap`.`package_id` AS `package_id`, `rap`.`package_name` AS `package_name` FROM (((((`bookings` `b` join `users` `u` on(`b`.`user_id` = `u`.`user_id`)) join `rooms` `r` on(`b`.`room_id` = `r`.`room_id`)) join `hotels` `h` on(`r`.`hotel_id` = `h`.`hotel_id`)) join `room_types` `rt` on(`r`.`room_type_id` = `rt`.`room_type_id`)) left join `room_amenity_packages` `rap` on(`b`.`package_id` = `rap`.`package_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `hotel_info_view`
--
DROP TABLE IF EXISTS `hotel_info_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `hotel_info_view`  AS SELECT `h`.`hotel_id` AS `hotel_id`, `h`.`hotel_name` AS `hotel_name`, `h`.`description` AS `description`, `h`.`address` AS `address`, `h`.`city` AS `city`, `h`.`star_rating` AS `star_rating`, `h`.`phone` AS `phone`, `h`.`email` AS `email`, `h`.`image_url` AS `image_url`, `c`.`category_name` AS `category_name`, `c`.`icon` AS `category_icon` FROM (`hotels` `h` left join `hotel_categories` `c` on(`h`.`category_id` = `c`.`category_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `room_availability_view`
--
DROP TABLE IF EXISTS `room_availability_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `room_availability_view`  AS SELECT `r`.`room_id` AS `room_id`, `r`.`hotel_id` AS `hotel_id`, `r`.`room_type_id` AS `room_type_id`, `r`.`room_number` AS `room_number`, `r`.`status` AS `status`, `h`.`hotel_name` AS `hotel_name`, `rt`.`type_name` AS `type_name`, `rt`.`base_price` AS `base_price`, (select count(0) from `bookings` `b` where `b`.`room_id` = `r`.`room_id` and `b`.`booking_status` in ('pending','confirmed') and `b`.`check_out_date` >= curdate()) AS `is_booked` FROM ((`rooms` `r` join `hotels` `h` on(`r`.`hotel_id` = `h`.`hotel_id`)) join `room_types` `rt` on(`r`.`room_type_id` = `rt`.`room_type_id`)) ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `amenities`
--
ALTER TABLE `amenities`
  ADD PRIMARY KEY (`amenity_id`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`booking_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `room_id` (`room_id`),
  ADD KEY `fk_bookings_package` (`package_id`);

--
-- Indexes for table `dynamic_pricing`
--
ALTER TABLE `dynamic_pricing`
  ADD PRIMARY KEY (`pricing_id`),
  ADD KEY `room_type_id` (`room_type_id`),
  ADD KEY `hotel_id` (`hotel_id`);

--
-- Indexes for table `hotels`
--
ALTER TABLE `hotels`
  ADD PRIMARY KEY (`hotel_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `hotel_categories`
--
ALTER TABLE `hotel_categories`
  ADD PRIMARY KEY (`category_id`),
  ADD UNIQUE KEY `category_name` (`category_name`);

--
-- Indexes for table `hotel_experiences`
--
ALTER TABLE `hotel_experiences`
  ADD PRIMARY KEY (`experience_id`),
  ADD KEY `hotel_id` (`hotel_id`);

--
-- Indexes for table `hotel_innovations`
--
ALTER TABLE `hotel_innovations`
  ADD PRIMARY KEY (`innovation_id`),
  ADD KEY `hotel_id` (`hotel_id`);

--
-- Indexes for table `hotel_sustainability`
--
ALTER TABLE `hotel_sustainability`
  ADD PRIMARY KEY (`sustainability_id`),
  ADD KEY `hotel_id` (`hotel_id`);

--
-- Indexes for table `inventory_items`
--
ALTER TABLE `inventory_items`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `hotel_id` (`hotel_id`);

--
-- Indexes for table `maintenance_requests`
--
ALTER TABLE `maintenance_requests`
  ADD PRIMARY KEY (`request_id`),
  ADD KEY `room_id` (`room_id`),
  ADD KEY `reported_by` (`reported_by`),
  ADD KEY `assigned_to` (`assigned_to`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `occupancy_stats`
--
ALTER TABLE `occupancy_stats`
  ADD PRIMARY KEY (`stat_id`),
  ADD UNIQUE KEY `hotel_id` (`hotel_id`,`date`);

--
-- Indexes for table `package_amenities`
--
ALTER TABLE `package_amenities`
  ADD PRIMARY KEY (`package_id`,`amenity_id`),
  ADD KEY `amenity_id` (`amenity_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`payment_method_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `payment_method_id` (`payment_method_id`);

--
-- Indexes for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`review_id`),
  ADD KEY `booking_id` (`booking_id`);

--
-- Indexes for table `rooms`
--
ALTER TABLE `rooms`
  ADD PRIMARY KEY (`room_id`),
  ADD UNIQUE KEY `hotel_id` (`hotel_id`,`room_number`),
  ADD KEY `room_type_id` (`room_type_id`);

--
-- Indexes for table `room_amenities`
--
ALTER TABLE `room_amenities`
  ADD PRIMARY KEY (`room_id`,`amenity_id`),
  ADD KEY `amenity_id` (`amenity_id`);

--
-- Indexes for table `room_amenity_packages`
--
ALTER TABLE `room_amenity_packages`
  ADD PRIMARY KEY (`package_id`),
  ADD KEY `room_type_id` (`room_type_id`);

--
-- Indexes for table `room_images`
--
ALTER TABLE `room_images`
  ADD PRIMARY KEY (`image_id`),
  ADD KEY `room_type_id` (`room_type_id`);

--
-- Indexes for table `room_sensor_data`
--
ALTER TABLE `room_sensor_data`
  ADD PRIMARY KEY (`data_id`),
  ADD KEY `room_id` (`room_id`);

--
-- Indexes for table `room_status_logs`
--
ALTER TABLE `room_status_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `room_id` (`room_id`),
  ADD KEY `changed_by` (`changed_by`);

--
-- Indexes for table `room_types`
--
ALTER TABLE `room_types`
  ADD PRIMARY KEY (`room_type_id`);

--
-- Indexes for table `smart_room_features`
--
ALTER TABLE `smart_room_features`
  ADD PRIMARY KEY (`feature_id`),
  ADD KEY `room_id` (`room_id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `user_activity_logs`
--
ALTER TABLE `user_activity_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `amenities`
--
ALTER TABLE `amenities`
  MODIFY `amenity_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `dynamic_pricing`
--
ALTER TABLE `dynamic_pricing`
  MODIFY `pricing_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `hotels`
--
ALTER TABLE `hotels`
  MODIFY `hotel_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `hotel_categories`
--
ALTER TABLE `hotel_categories`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `hotel_experiences`
--
ALTER TABLE `hotel_experiences`
  MODIFY `experience_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `hotel_innovations`
--
ALTER TABLE `hotel_innovations`
  MODIFY `innovation_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `hotel_sustainability`
--
ALTER TABLE `hotel_sustainability`
  MODIFY `sustainability_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `inventory_items`
--
ALTER TABLE `inventory_items`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `maintenance_requests`
--
ALTER TABLE `maintenance_requests`
  MODIFY `request_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `occupancy_stats`
--
ALTER TABLE `occupancy_stats`
  MODIFY `stat_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `payment_method_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `reviews`
--
ALTER TABLE `reviews`
  MODIFY `review_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rooms`
--
ALTER TABLE `rooms`
  MODIFY `room_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `room_amenity_packages`
--
ALTER TABLE `room_amenity_packages`
  MODIFY `package_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `room_images`
--
ALTER TABLE `room_images`
  MODIFY `image_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `room_sensor_data`
--
ALTER TABLE `room_sensor_data`
  MODIFY `data_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `room_status_logs`
--
ALTER TABLE `room_status_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `room_types`
--
ALTER TABLE `room_types`
  MODIFY `room_type_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `smart_room_features`
--
ALTER TABLE `smart_room_features`
  MODIFY `feature_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_activity_logs`
--
ALTER TABLE `user_activity_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_bookings_package` FOREIGN KEY (`package_id`) REFERENCES `room_amenity_packages` (`package_id`) ON DELETE SET NULL;

--
-- Constraints for table `dynamic_pricing`
--
ALTER TABLE `dynamic_pricing`
  ADD CONSTRAINT `dynamic_pricing_ibfk_1` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`room_type_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `dynamic_pricing_ibfk_2` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE;

--
-- Constraints for table `hotels`
--
ALTER TABLE `hotels`
  ADD CONSTRAINT `hotels_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `hotel_categories` (`category_id`);

--
-- Constraints for table `hotel_experiences`
--
ALTER TABLE `hotel_experiences`
  ADD CONSTRAINT `hotel_experiences_ibfk_1` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE;

--
-- Constraints for table `hotel_innovations`
--
ALTER TABLE `hotel_innovations`
  ADD CONSTRAINT `hotel_innovations_ibfk_1` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE;

--
-- Constraints for table `hotel_sustainability`
--
ALTER TABLE `hotel_sustainability`
  ADD CONSTRAINT `hotel_sustainability_ibfk_1` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE;

-- Create a view to show all rooms with their details
CREATE OR REPLACE VIEW all_rooms_view AS
SELECT r.room_id, r.hotel_id, r.room_type_id, r.room_number, r.floor,
       r.description, r.price, r.capacity, r.image, r.status,
       h.hotel_name, h.city, h.image_url AS hotel_image,
       rt.type_name, rt.base_price, rt.description AS type_description
FROM rooms r
JOIN hotels h ON r.hotel_id = h.hotel_id
JOIN room_types rt ON r.room_type_id = rt.room_type_id;

-- Modify the room_availability_view to include all rooms regardless of booking status
DROP VIEW IF EXISTS room_availability_view;
CREATE VIEW room_availability_view AS
SELECT r.room_id, r.hotel_id, r.room_type_id, r.room_number, r.status,
       h.hotel_name, rt.type_name, rt.base_price,
       (SELECT COUNT(*) FROM bookings b
        WHERE b.room_id = r.room_id
        AND b.booking_status IN ('pending','confirmed')
        AND b.check_out_date >= CURDATE()) AS is_booked
FROM rooms r
JOIN hotels h ON r.hotel_id = h.hotel_id
JOIN room_types rt ON r.room_type_id = rt.room_type_id;

--
-- Constraints for table `inventory_items`
--
ALTER TABLE `inventory_items`
  ADD CONSTRAINT `inventory_items_ibfk_1` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE;

--
-- Constraints for table `maintenance_requests`
--
ALTER TABLE `maintenance_requests`
  ADD CONSTRAINT `maintenance_requests_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `maintenance_requests_ibfk_2` FOREIGN KEY (`reported_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `maintenance_requests_ibfk_3` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `occupancy_stats`
--
ALTER TABLE `occupancy_stats`
  ADD CONSTRAINT `occupancy_stats_ibfk_1` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE;

--
-- Constraints for table `package_amenities`
--
ALTER TABLE `package_amenities`
  ADD CONSTRAINT `package_amenities_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `room_amenity_packages` (`package_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `package_amenities_ibfk_2` FOREIGN KEY (`amenity_id`) REFERENCES `amenities` (`amenity_id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_3` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`payment_method_id`) ON DELETE CASCADE;

--
-- Constraints for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  ADD CONSTRAINT `remember_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `reviews`
--
ALTER TABLE `reviews`
  ADD CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE;

--
-- Constraints for table `rooms`
--
ALTER TABLE `rooms`
  ADD CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`hotel_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `rooms_ibfk_2` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`room_type_id`) ON DELETE CASCADE;

--
-- Constraints for table `room_amenities`
--
ALTER TABLE `room_amenities`
  ADD CONSTRAINT `room_amenities_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `room_amenities_ibfk_2` FOREIGN KEY (`amenity_id`) REFERENCES `amenities` (`amenity_id`) ON DELETE CASCADE;

--
-- Constraints for table `room_amenity_packages`
--
ALTER TABLE `room_amenity_packages`
  ADD CONSTRAINT `room_amenity_packages_ibfk_1` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`room_type_id`) ON DELETE CASCADE;

--
-- Constraints for table `room_images`
--
ALTER TABLE `room_images`
  ADD CONSTRAINT `room_images_ibfk_1` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`room_type_id`) ON DELETE CASCADE;

--
-- Constraints for table `room_sensor_data`
--
ALTER TABLE `room_sensor_data`
  ADD CONSTRAINT `room_sensor_data_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE;

--
-- Constraints for table `room_status_logs`
--
ALTER TABLE `room_status_logs`
  ADD CONSTRAINT `room_status_logs_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `room_status_logs_ibfk_2` FOREIGN KEY (`changed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `smart_room_features`
--
ALTER TABLE `smart_room_features`
  ADD CONSTRAINT `smart_room_features_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE CASCADE;

--
-- Constraints for table `user_activity_logs`
--
ALTER TABLE `user_activity_logs`
  ADD CONSTRAINT `user_activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- Add rooms 1-20 for each room type in each hotel (if not already present)
-- This ensures each hotel has a good selection of rooms for booking

-- Standard Room (room_type_id=1) - 20 rooms per hotel
INSERT IGNORE INTO rooms (hotel_id, room_type_id, room_number, floor, description, price, capacity, image, status)
SELECT h.hotel_id, 1, LPAD(n,3,'0'), FLOOR((n-1)/10)+1, 'Comfortable standard room with modern amenities', 1500.00, 2, NULL, 'available'
FROM (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
      UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
      UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15
      UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20) AS nums
CROSS JOIN (SELECT hotel_id FROM hotels) AS h;

-- Deluxe Room (room_type_id=2) - 15 rooms per hotel
INSERT IGNORE INTO rooms (hotel_id, room_type_id, room_number, floor, description, price, capacity, image, status)
SELECT h.hotel_id, 2, LPAD(n+100,3,'0'), FLOOR((n-1)/10)+2, 'Spacious deluxe room with premium amenities', 2500.00, 2, NULL, 'available'
FROM (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
      UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
      UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15) AS nums
CROSS JOIN (SELECT hotel_id FROM hotels) AS h;

-- Suite (room_type_id=3) - 10 rooms per hotel
INSERT IGNORE INTO rooms (hotel_id, room_type_id, room_number, floor, description, price, capacity, image, status)
SELECT h.hotel_id, 3, LPAD(n+200,3,'0'), FLOOR((n-1)/5)+3, 'Luxurious suite with separate living area', 3500.00, 3, NULL, 'available'
FROM (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
      UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10) AS nums
CROSS JOIN (SELECT hotel_id FROM hotels) AS h;

-- Family Room (room_type_id=4) - 10 rooms per hotel
INSERT IGNORE INTO rooms (hotel_id, room_type_id, room_number, floor, description, price, capacity, image, status)
SELECT h.hotel_id, 4, LPAD(n+300,3,'0'), FLOOR((n-1)/5)+2, 'Spacious family room perfect for families', 4000.00, 4, NULL, 'available'
FROM (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
      UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10) AS nums
CROSS JOIN (SELECT hotel_id FROM hotels) AS h;

-- Executive Room (room_type_id=5) - 5 rooms per hotel
INSERT IGNORE INTO rooms (hotel_id, room_type_id, room_number, floor, description, price, capacity, image, status)
SELECT h.hotel_id, 5, LPAD(n+400,3,'0'), 4, 'Executive room with business amenities', 3000.00, 2, NULL, 'available'
FROM (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5) AS nums
CROSS JOIN (SELECT hotel_id FROM hotels) AS h;

