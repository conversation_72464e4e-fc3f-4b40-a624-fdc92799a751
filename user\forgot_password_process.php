<?php
// Forgot Password Process Handler
include 'includes/db.php';
include '../includes/mailer.php';

// Check if email is provided
if (!isset($_POST['email']) || empty($_POST['email'])) {
    header('Location: forgot_password.php?msg=error');
    exit;
}

$email = trim($_POST['email']);

// Check if email exists in the database
$stmt = $mysqli->prepare("SELECT user_id, first_name FROM users WHERE email = ?");
$stmt->bind_param('s', $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Email not found
    header('Location: forgot_password.php?msg=error');
    exit;
}

// Get user information
$user = $result->fetch_assoc();
$user_id = $user['user_id'];
$first_name = $user['first_name'];

// Generate a unique token
$token = bin2hex(random_bytes(32));
$expires = date('Y-m-d H:i:s', strtotime('+24 hours'));

// Check if password_resets table exists
$table_check = $mysqli->query("SHOW TABLES LIKE 'password_resets'");
if ($table_check->num_rows === 0) {
    // Create password_resets table if it doesn't exist
    $mysqli->query("CREATE TABLE password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(64) NOT NULL,
        expires DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
    )");
}

// Delete any existing tokens for this user
$mysqli->query("DELETE FROM password_resets WHERE user_id = {$user_id}");

// Insert the new token
$stmt = $mysqli->prepare("INSERT INTO password_resets (user_id, token, expires) VALUES (?, ?, ?)");
$stmt->bind_param('iss', $user_id, $token, $expires);
$stmt->execute();
$stmt->close();

// Send password reset email
$result = sendPasswordResetEmail($mysqli, $email, $token, $user_id);

if ($result['success']) {
    // Log the activity
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $mysqli->query("INSERT INTO user_activity_logs (user_id, action, description, ip_address, user_agent) 
                   VALUES ({$user_id}, 'password_reset_request', 'Password reset requested', '{$ip}', '{$ua}')");
    
    header('Location: forgot_password.php?msg=success');
} else {
    // Email sending failed
    header('Location: forgot_password.php?msg=mail_error');
}
exit;
