/*
 * SmartHotel - Modern User Interface Styles
 * This CSS file provides a modern, responsive design for the user interface
 * Following the criteria for excellent design (UI/UX)
 */

:root {
    --primary-color: #4e73df;
    --primary-dark: #3a56b0;
    --secondary-color: #1cc88a;
    --secondary-dark: #169e6c;
    --accent-color: #f6c23e;
    --danger-color: #e74a3b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fc;
    --text-color: #333;
    --gray-color: #858796;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

/* Base Styles */
body {
    font-family: 'Nunito', 'Segoe UI', Arial, sans-serif;
    color: var(--text-color);
    background-color: #f8f9fc;
    line-height: 1.6;
}

a {
    color: var(--primary-color);
    transition: all var(--transition-speed);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Ultra-Modern Glass Morphism Navbar */
.modern-navbar {
    background: rgba(255, 255, 255, 0.08) !important;
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding: 1rem 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.modern-navbar.scrolled {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Modern Brand Design */
.modern-brand {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 800;
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
    letter-spacing: -0.02em;
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #00d4aa 0%, #00a085 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
    transition: all 0.3s ease;
}

.brand-icon i {
    font-size: 1.2rem;
    color: white;
}

.brand-text {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modern-brand:hover {
    transform: translateY(-2px);
    color: white !important;
}

.modern-brand:hover .brand-icon {
    transform: rotate(5deg) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
}

/* Modern Navigation Links */
.navbar-nav .nav-link {
    font-weight: 600;
    padding: 0.8rem 1.2rem !important;
    position: relative;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 12px;
    margin: 0 0.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link i {
    margin-right: 8px;
    font-size: 0.9rem;
}

/* Modern Dropdown */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item {
    border-radius: 8px;
    padding: 0.7rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

/* Modern Toggle Button */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 70vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-align: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8), rgba(52, 152, 219, 0.7));
    z-index: 1;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all var(--transition-speed);
    overflow: hidden;
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.card-text {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
}

.hotel-card .card-img-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 1.5rem;
}

.hotel-card .card-title {
    color: #fff;
    margin-bottom: 0.5rem;
}

.hotel-card .card-text {
    color: rgba(255, 255, 255, 0.8);
}

.hotel-card .hotel-rating {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--accent-color);
    color: var(--dark-color);
    font-weight: 700;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.9rem;
}

.hotel-card .hotel-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-top: 0.5rem;
}

.hotel-card .hotel-location {
    display: flex;
    align-items: center;
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.hotel-card .hotel-location i {
    margin-right: 5px;
    color: var(--primary-color);
}

/* Form Styles */
.form-control {
    border: 1px solid #e3e6f0;
    border-radius: var(--border-radius);
    padding: 0.8rem 1rem;
    font-size: 0.95rem;
    transition: all var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Button Styles */
.btn {
    border-radius: 50px;
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: all var(--transition-speed);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
}

.btn-success {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(28, 200, 138, 0.3);
}

/* Footer Styles */
footer {
    background: var(--dark-color);
    color: #fff;
    padding: 3rem 0 2rem;
    margin-top: 5rem;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 250px;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.footer-section h5 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #fff;
    position: relative;
    padding-bottom: 0.8rem;
}

.footer-section h5::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: all var(--transition-speed);
}

.footer-links a:hover {
    color: #fff;
    padding-left: 5px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .footer-section {
        flex: 0 0 100%;
    }
}
