<?php
// Email Settings Page for Admin Panel
// Allows configuration and testing of PHPMailer settings
// Functionality: Configure SMTP settings, test email sending
// Design: Modern, responsive, visually consistent
// Code Neatness: Well-structured, meaningful comments

session_start();
include 'includes/db.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Default settings
$settings = [
    'smtp_host' => 'smtp.example.com',
    'smtp_port' => '587',
    'smtp_secure' => 'tls',
    'smtp_auth' => '1',
    'smtp_username' => '<EMAIL>',
    'smtp_password' => '',
    'from_email' => '<EMAIL>',
    'from_name' => 'Hotel Admin',
];

// Load settings from database if they exist
$result = $mysqli->query("SELECT * FROM system_settings WHERE setting_group = 'email' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $settings_data = json_decode($row['settings_data'], true);
    if (is_array($settings_data)) {
        $settings = array_merge($settings, $settings_data);
    }
}

$msg = '';
$error = '';
$test_result = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Save settings
    if (isset($_POST['save_settings'])) {
        $new_settings = [
            'smtp_host' => trim($_POST['smtp_host'] ?? ''),
            'smtp_port' => trim($_POST['smtp_port'] ?? ''),
            'smtp_secure' => $_POST['smtp_secure'] ?? 'tls',
            'smtp_auth' => isset($_POST['smtp_auth']) ? '1' : '0',
            'smtp_username' => trim($_POST['smtp_username'] ?? ''),
            'smtp_password' => trim($_POST['smtp_password'] ?? $settings['smtp_password']),
            'from_email' => trim($_POST['from_email'] ?? ''),
            'from_name' => trim($_POST['from_name'] ?? ''),
        ];

        // Only update password if a new one is provided
        if (empty($new_settings['smtp_password'])) {
            $new_settings['smtp_password'] = $settings['smtp_password'];
        }

        $settings_json = json_encode($new_settings);

        // Check if settings already exist
        if ($result && $result->num_rows > 0) {
            $stmt = $mysqli->prepare("UPDATE system_settings SET settings_data = ? WHERE setting_group = 'email'");
            $stmt->bind_param('s', $settings_json);
        } else {
            $stmt = $mysqli->prepare("INSERT INTO system_settings (setting_group, settings_data) VALUES ('email', ?)");
            $stmt->bind_param('s', $settings_json);
        }

        if ($stmt->execute()) {
            $settings = $new_settings;
            $msg = "Email settings saved successfully!";
        } else {
            $error = "Error saving settings: " . $mysqli->error;
        }
        $stmt->close();
    }

    // Test email
    if (isset($_POST['test_email'])) {
        $test_email = trim($_POST['test_recipient'] ?? '');
        if (empty($test_email)) {
            $error = "Please enter a recipient email address for testing.";
        } else {
            // Send test email using current settings
            $test_result = sendTestEmail($test_email, $settings);
        }
    }
}

// Function to send test email
function sendTestEmail($recipient, $settings) {
    // Include PHPMailer
    require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
    require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
    require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';

    $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->Port = $settings['smtp_port'];

        if ($settings['smtp_secure'] === 'tls') {
            $mail->SMTPSecure = \PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($settings['smtp_secure'] === 'ssl') {
            $mail->SMTPSecure = \PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = false;
            $mail->SMTPAutoTLS = false;
        }

        if ($settings['smtp_auth'] === '1') {
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];
        } else {
            $mail->SMTPAuth = false;
        }

        // Enable verbose debug output if needed
        // $mail->SMTPDebug = 2;

        // Recipients
        $mail->setFrom($settings['from_email'], $settings['from_name']);
        $mail->addAddress($recipient);

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from Hotel Admin Panel';
        $mail->Body = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
                <h2 style="color: #4e73df; text-align: center;">Test Email</h2>
                <p>This is a test email sent from your Hotel Admin Panel.</p>
                <p>If you received this email, your email settings are configured correctly!</p>
                <div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <p style="margin: 0;"><strong>SMTP Host:</strong> ' . htmlspecialchars($settings['smtp_host']) . '</p>
                    <p style="margin: 5px 0;"><strong>SMTP Port:</strong> ' . htmlspecialchars($settings['smtp_port']) . '</p>
                    <p style="margin: 5px 0;"><strong>SMTP Security:</strong> ' . htmlspecialchars($settings['smtp_secure']) . '</p>
                    <p style="margin: 5px 0;"><strong>From Email:</strong> ' . htmlspecialchars($settings['from_email']) . '</p>
                    <p style="margin: 5px 0;"><strong>From Name:</strong> ' . htmlspecialchars($settings['from_name']) . '</p>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #858796; font-size: 14px;">
                    Sent on: ' . date('Y-m-d H:i:s') . '
                </p>
            </div>
        ';

        // Create plain text version
        $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />', '</p>'], "\n", $mail->Body));

        $mail->send();

        // Log the email in the database
        logTestEmail($recipient, true);

        return [
            'success' => true,
            'message' => "Test email sent successfully to {$recipient}!"
        ];
    } catch (Exception $e) {
        // Log the error
        logTestEmail($recipient, false, $mail->ErrorInfo);

        return [
            'success' => false,
            'message' => "Error sending test email: {$mail->ErrorInfo}"
        ];
    }
}

// Function to log test emails
function logTestEmail($recipient, $success, $error_message = '') {
    global $mysqli;

    // Check if email_logs table exists
    $table_check = $mysqli->query("SHOW TABLES LIKE 'email_logs'");

    if ($table_check && $table_check->num_rows == 0) {
        // Create the table if it doesn't exist
        $mysqli->query("CREATE TABLE IF NOT EXISTS email_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            recipient VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
    }

    // Insert log entry
    $subject = 'Test Email from Hotel Admin Panel';
    $status = $success ? 'success' : 'error';

    $stmt = $mysqli->prepare("INSERT INTO email_logs (recipient, subject, status, error_message) VALUES (?, ?, ?, ?)");
    $stmt->bind_param('ssss', $recipient, $subject, $status, $error_message);
    $stmt->execute();
    $stmt->close();
}

include 'includes/header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h2><i class="fa fa-envelope"></i> Email Settings</h2>
        <div class="breadcrumb">
            <span><i class="fa fa-home"></i> Home</span>
            <span class="separator">/</span>
            <span>Email Settings</span>
        </div>
    </div>

    <?php if ($msg): ?>
    <div class="alert alert-success">
        <i class="fa fa-check-circle"></i> <?= htmlspecialchars($msg) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if ($test_result): ?>
    <div class="alert <?= $test_result['success'] ? 'alert-success' : 'alert-danger' ?>">
        <i class="fa <?= $test_result['success'] ? 'fa-check-circle' : 'fa-exclamation-circle' ?>"></i>
        <?= htmlspecialchars($test_result['message']) ?>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h4><i class="fa fa-cog"></i> SMTP Configuration</h4>
        </div>

        <form method="post" class="settings-form">
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="smtp_host">SMTP Host</label>
                    <input type="text" id="smtp_host" name="smtp_host" value="<?= htmlspecialchars($settings['smtp_host']) ?>" required>
                </div>
                <div class="form-group col-md-6">
                    <label for="smtp_port">SMTP Port</label>
                    <input type="text" id="smtp_port" name="smtp_port" value="<?= htmlspecialchars($settings['smtp_port']) ?>" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="smtp_secure">SMTP Security</label>
                    <select id="smtp_secure" name="smtp_secure">
                        <option value="tls" <?= $settings['smtp_secure'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                        <option value="ssl" <?= $settings['smtp_secure'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                        <option value="none" <?= $settings['smtp_secure'] === 'none' ? 'selected' : '' ?>>None</option>
                    </select>
                </div>
                <div class="form-group col-md-6">
                    <label class="checkbox-container">
                        <input type="checkbox" id="smtp_auth" name="smtp_auth" <?= $settings['smtp_auth'] === '1' ? 'checked' : '' ?>>
                        <span class="checkmark"></span>
                        Require Authentication
                    </label>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="smtp_username">SMTP Username</label>
                    <input type="text" id="smtp_username" name="smtp_username" value="<?= htmlspecialchars($settings['smtp_username']) ?>">
                </div>
                <div class="form-group col-md-6">
                    <label for="smtp_password">SMTP Password</label>
                    <input type="password" id="smtp_password" name="smtp_password" placeholder="Leave empty to keep current password">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="from_email">From Email</label>
                    <input type="email" id="from_email" name="from_email" value="<?= htmlspecialchars($settings['from_email']) ?>" required>
                </div>
                <div class="form-group col-md-6">
                    <label for="from_name">From Name</label>
                    <input type="text" id="from_name" name="from_name" value="<?= htmlspecialchars($settings['from_name']) ?>" required>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" name="save_settings" class="btn btn-primary">
                    <i class="fa fa-save"></i> Save Settings
                </button>
            </div>
        </form>
    </div>

    <div class="card">
        <div class="card-header">
            <h4><i class="fa fa-paper-plane"></i> Test Email</h4>
        </div>

        <form method="post" class="test-email-form">
            <div class="form-row">
                <div class="form-group col-md-12">
                    <label for="test_recipient">Recipient Email</label>
                    <input type="email" id="test_recipient" name="test_recipient" required>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" name="test_email" class="btn btn-success">
                    <i class="fa fa-paper-plane"></i> Send Test Email
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.1);
    color: var(--secondary-color);
    border-left: 4px solid var(--secondary-color);
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 10px;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 10px;
}

.form-actions {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e3e6f0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    margin-top: 2rem;
    cursor: pointer;
}

.checkbox-container input {
    width: auto;
    margin-right: 10px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
