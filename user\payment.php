<?php
// User Payment Page for Booking
include 'includes/header.php';
include 'includes/db.php';
// Session is already started in header.php
$user_id = $_SESSION['user_id'] ?? 0;
$booking_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$user_id || !$booking_id) { header('Location: my_bookings.php?msg=Invalid+request'); exit; }
$res = $mysqli->query("SELECT b.*, r.room_number, h.hotel_name FROM bookings b JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.booking_id=$booking_id AND b.user_id=$user_id LIMIT 1");
$booking = $res->fetch_assoc();
if (!$booking || $booking['payment_status'] == 'paid') { header('Location: my_bookings.php?msg=Invalid+or+already+paid'); exit; }
// Get payment methods
$methods = $mysqli->query("SELECT * FROM payment_methods WHERE is_active=1");
?>
<div class="container" style="margin-top:2rem;max-width:600px;">
    <h2 class="mb-4" style="font-weight:600;">Pay for Booking #<?=intval($booking_id)?></h2>
    <div class="card shadow mb-4">
        <div class="card-body">
            <h5>Hotel: <?=htmlspecialchars($booking['hotel_name'])?></h5>
            <h6>Room: <?=htmlspecialchars($booking['room_number'])?></h6>
            <div>Check-in: <?=htmlspecialchars($booking['check_in_date'])?></div>
            <div>Check-out: <?=htmlspecialchars($booking['check_out_date'])?></div>
            <div>Total: <b>₱<?=number_format($booking['total_price'],2)?></b></div>
            <form method="post" action="process_payment.php">
                <input type="hidden" name="booking_id" value="<?=intval($booking_id)?>">
                <div class="mb-3 mt-3">
                    <label class="form-label">Select Payment Method</label>
                    <select class="form-select" name="payment_method_id" required>
                        <option value="">Choose...</option>
                        <?php while($m = $methods->fetch_assoc()): ?>
                        <option value="<?=$m['payment_method_id']?>"><?=htmlspecialchars($m['method_name'])?></option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <button class="btn btn-success w-100" type="submit">Pay Now</button>
            </form>
        </div>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
