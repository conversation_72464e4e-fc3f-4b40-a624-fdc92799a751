<?php
// User Payment Page for Booking
include 'includes/header.php';
include 'includes/db.php';
// Session is already started in header.php
$user_id = $_SESSION['user_id'] ?? 0;
$booking_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id || !$booking_id) {
    header('Location: my_bookings.php?msg=Invalid+request');
    exit;
}

// Get booking details with room type information
$stmt = $mysqli->prepare("
    SELECT b.*, r.room_number, h.hotel_name, h.phone as hotel_phone, h.address, h.city,
           rt.type_name, rt.description as room_description,
           u.first_name, u.last_name, u.email
    FROM bookings b
    JOIN rooms r ON b.room_id = r.room_id
    JOIN hotels h ON r.hotel_id = h.hotel_id
    JOIN room_types rt ON r.room_type_id = rt.room_type_id
    JOIN users u ON b.user_id = u.user_id
    WHERE b.booking_id = ? AND b.user_id = ?
    LIMIT 1
");
$stmt->bind_param('ii', $booking_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();
$booking = $result->fetch_assoc();
$stmt->close();

if (!$booking || $booking['payment_status'] == 'paid') {
    header('Location: my_bookings.php?msg=Invalid+or+already+paid');
    exit;
}

// Calculate nights
$checkin = new DateTime($booking['check_in_date']);
$checkout = new DateTime($booking['check_out_date']);
$nights = $checkin->diff($checkout)->days;

// Get payment methods
$methods = $mysqli->query("SELECT * FROM payment_methods WHERE is_active=1 ORDER BY payment_method_id");
?>

<style>
.payment-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.payment-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.payment-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.payment-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.payment-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 1;
}

.payment-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.booking-summary {
    background: #f8f9fc;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #4e73df;
}

.booking-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.booking-detail:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
    color: #1cc88a;
}

.booking-detail .label {
    color: #5a5c69;
    font-weight: 500;
}

.booking-detail .value {
    color: #2c3e50;
    font-weight: 600;
}

.payment-method-card {
    border: 2px solid #e3e6f0;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method-card:hover {
    border-color: #4e73df;
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.1);
    transform: translateY(-2px);
}

.payment-method-card.selected {
    border-color: #4e73df;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
}

.payment-method-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.payment-method-card:not(.selected) .payment-method-icon {
    background: #f8f9fc;
    color: #4e73df;
}

.payment-method-card.selected .payment-method-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.pay-button {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(28, 200, 138, 0.3);
}

.pay-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(28, 200, 138, 0.4);
}

.pay-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.security-info {
    background: #e8f5e8;
    border: 1px solid #d4edda;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1.5rem;
    text-align: center;
}

.security-info i {
    color: #1cc88a;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.breadcrumb-nav a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
}

.breadcrumb-nav a:hover {
    opacity: 1;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e73df;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.payment-details {
    margin-top: 1.5rem;
}

.payment-form {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.payment-steps {
    padding-left: 1.2rem;
}

.payment-steps li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.payment-notes {
    padding-left: 1.2rem;
}

.payment-notes li {
    margin-bottom: 0.3rem;
    line-height: 1.4;
}

.detail-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.card-logos img {
    transition: transform 0.2s ease;
}

.card-logos img:hover {
    transform: scale(1.1);
}

.qr-code-container {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
}

.bank-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.cash-details {
    background: #fff3cd;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

@media (max-width: 768px) {
    .payment-container {
        padding: 1rem 0;
    }

    .payment-header h1 {
        font-size: 1.5rem;
    }

    .booking-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .qr-code-container img {
        max-width: 200px !important;
    }
}
</style>

<div class="payment-container">
    <div class="container">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-nav">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="my_bookings.php"><i class="fas fa-calendar-check me-1"></i>My Bookings</a></li>
                    <li class="breadcrumb-item active text-white" aria-current="page">Payment</li>
                </ol>
            </nav>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8 col-xl-7">
                <div class="payment-card">
                    <!-- Payment Header -->
                    <div class="payment-header">
                        <h1><i class="fas fa-credit-card me-2"></i>Secure Payment</h1>
                        <p>Complete your booking payment for Booking #<?= $booking_id ?></p>
                    </div>

                    <div class="p-4">
                        <!-- Booking Summary -->
                        <div class="booking-summary">
                            <h4 class="mb-3"><i class="fas fa-file-invoice me-2 text-primary"></i>Booking Summary</h4>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-hotel me-2"></i>Hotel</span>
                                <span class="value"><?= htmlspecialchars($booking['hotel_name']) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-bed me-2"></i>Room</span>
                                <span class="value"><?= htmlspecialchars($booking['room_number']) ?> (<?= htmlspecialchars($booking['type_name']) ?>)</span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-calendar-alt me-2"></i>Check-in</span>
                                <span class="value"><?= date('M j, Y', strtotime($booking['check_in_date'])) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-calendar-alt me-2"></i>Check-out</span>
                                <span class="value"><?= date('M j, Y', strtotime($booking['check_out_date'])) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-moon me-2"></i>Duration</span>
                                <span class="value"><?= $nights ?> night<?= $nights > 1 ? 's' : '' ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-user me-2"></i>Guest</span>
                                <span class="value"><?= htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-peso-sign me-2"></i>Total Amount</span>
                                <span class="value">₱<?= number_format($booking['total_price'], 2) ?></span>
                            </div>
                        </div>

                        <!-- Payment Form -->
                        <form id="payment-form" method="post" action="process_payment.php">
                            <input type="hidden" name="booking_id" value="<?= $booking_id ?>">
                            <input type="hidden" name="gcash_reference" id="gcash_reference_hidden">
                            <input type="hidden" name="bank_reference" id="bank_reference_hidden">
                            <input type="hidden" name="card_number" id="card_number_hidden">

                            <h4 class="mb-3"><i class="fas fa-credit-card me-2 text-primary"></i>Select Payment Method</h4>

                            <div class="payment-methods mb-4">
                                <?php
                                $method_icons = [
                                    'Credit/Debit Card' => 'fas fa-credit-card',
                                    'PayPal' => 'fab fa-paypal',
                                    'GCash' => 'fas fa-mobile-alt',
                                    'Bank Transfer' => 'fas fa-university',
                                    'Cash on Arrival' => 'fas fa-money-bill-wave'
                                ];

                                $methods->data_seek(0); // Reset pointer
                                while($method = $methods->fetch_assoc()):
                                    $icon = $method_icons[$method['method_name']] ?? 'fas fa-money-bill';
                                    $method_id = $method['payment_method_id'];
                                    $method_name = $method['method_name'];
                                ?>
                                <div class="payment-method-card" data-method="<?= $method_id ?>" data-method-name="<?= htmlspecialchars($method_name) ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="payment-method-icon">
                                            <i class="<?= $icon ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?= htmlspecialchars($method_name) ?></h6>
                                            <small class="opacity-75"><?= htmlspecialchars($method['description'] ?? 'Secure payment processing') ?></small>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method_id"
                                                   value="<?= $method_id ?>" id="method_<?= $method_id ?>" required>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>

                            <!-- Payment Method Details -->
                            <div id="payment-details" class="payment-details mb-4" style="display: none;">
                                <!-- Credit Card Form -->
                                <div id="credit-card-form" class="payment-form" style="display: none;">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Credit/Debit Card Information</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label class="form-label">Card Number</label>
                                                    <input type="text" class="form-control" placeholder="1234 5678 9012 3456" maxlength="19" id="card-number">
                                                    <div class="card-logos mt-2">
                                                        <img src="https://img.icons8.com/color/32/000000/visa.png" alt="Visa" class="me-2">
                                                        <img src="https://img.icons8.com/color/32/000000/mastercard.png" alt="Mastercard" class="me-2">
                                                        <img src="https://img.icons8.com/color/32/000000/amex.png" alt="Amex">
                                                    </div>
                                                </div>
                                                <div class="col-md-8 mb-3">
                                                    <label class="form-label">Cardholder Name</label>
                                                    <input type="text" class="form-control" placeholder="John Doe" value="<?= htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']) ?>">
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label class="form-label">Expiry Date</label>
                                                    <input type="text" class="form-control" placeholder="MM/YY" maxlength="5" id="expiry-date">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">CVV</label>
                                                    <input type="text" class="form-control" placeholder="123" maxlength="4" id="cvv">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Billing ZIP</label>
                                                    <input type="text" class="form-control" placeholder="12345">
                                                </div>
                                            </div>
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Your card will be charged ₱<?= number_format($booking['total_price'], 2) ?> immediately.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- PayPal Form -->
                                <div id="paypal-form" class="payment-form" style="display: none;">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header" style="background: #0070ba; color: white;">
                                            <h5 class="mb-0"><i class="fab fa-paypal me-2"></i>PayPal Payment</h5>
                                        </div>
                                        <div class="card-body text-center">
                                            <div class="paypal-logo mb-3">
                                                <img src="https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-200px.png" alt="PayPal" style="height: 60px;">
                                            </div>
                                            <p class="lead">You will be redirected to PayPal to complete your payment of <strong>₱<?= number_format($booking['total_price'], 2) ?></strong></p>
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                After clicking "Pay Now", you'll be taken to PayPal's secure payment page.
                                            </div>
                                            <div class="paypal-benefits">
                                                <div class="row text-start">
                                                    <div class="col-md-6">
                                                        <p><i class="fas fa-check text-success me-2"></i>Buyer Protection</p>
                                                        <p><i class="fas fa-check text-success me-2"></i>Secure Checkout</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><i class="fas fa-check text-success me-2"></i>No Card Details Required</p>
                                                        <p><i class="fas fa-check text-success me-2"></i>Instant Payment</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- GCash Form -->
                                <div id="gcash-form" class="payment-form" style="display: none;">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header" style="background: #007dff; color: white;">
                                            <h5 class="mb-0"><i class="fas fa-mobile-alt me-2"></i>GCash Payment</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="mb-3">Scan QR Code with GCash App</h6>
                                                    <div class="qr-code-container text-center mb-3">
                                                        <img src="images/instapay_qr_code.jpg" alt="GCash QR Code" class="img-fluid" style="max-width: 250px; border: 2px solid #007dff; border-radius: 10px;">
                                                    </div>
                                                    <div class="alert alert-primary">
                                                        <strong>Amount to Pay:</strong> ₱<?= number_format($booking['total_price'], 2) ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6 class="mb-3">Payment Instructions</h6>
                                                    <ol class="payment-steps">
                                                        <li>Open your GCash app</li>
                                                        <li>Tap "Scan QR" or "Pay QR"</li>
                                                        <li>Scan the QR code on the left</li>
                                                        <li>Enter amount: ₱<?= number_format($booking['total_price'], 2) ?></li>
                                                        <li>Add reference: <strong>BOOKING-<?= $booking_id ?></strong></li>
                                                        <li>Complete the payment</li>
                                                        <li>Take a screenshot of the receipt</li>
                                                    </ol>

                                                    <div class="mt-3">
                                                        <label class="form-label">GCash Reference Number</label>
                                                        <input type="text" class="form-control" placeholder="Enter GCash reference number" id="gcash-reference">
                                                        <small class="text-muted">Enter the reference number from your GCash receipt</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bank Transfer Form -->
                                <div id="bank-transfer-form" class="payment-form" style="display: none;">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0"><i class="fas fa-university me-2"></i>Bank Transfer</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="mb-3">Bank Account Details</h6>
                                                    <div class="bank-details">
                                                        <div class="detail-item mb-2">
                                                            <strong>Bank Name:</strong> BPI (Bank of the Philippine Islands)
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Account Name:</strong> SmartHotel Cabadbaran
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Account Number:</strong> 1234-5678-90
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Amount:</strong> ₱<?= number_format($booking['total_price'], 2) ?>
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Reference:</strong> BOOKING-<?= $booking_id ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6 class="mb-3">Transfer Instructions</h6>
                                                    <ol class="payment-steps">
                                                        <li>Log in to your online banking</li>
                                                        <li>Select "Transfer to Other Bank"</li>
                                                        <li>Enter the bank details on the left</li>
                                                        <li>Use reference: <strong>BOOKING-<?= $booking_id ?></strong></li>
                                                        <li>Complete the transfer</li>
                                                        <li>Save the transaction receipt</li>
                                                    </ol>

                                                    <div class="mt-3">
                                                        <label class="form-label">Bank Reference Number</label>
                                                        <input type="text" class="form-control" placeholder="Enter bank reference number" id="bank-reference">
                                                        <small class="text-muted">Enter the reference number from your bank receipt</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-warning mt-3">
                                                <i class="fas fa-clock me-2"></i>
                                                Bank transfers may take 1-3 business days to process. Your booking will be confirmed once payment is received.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cash on Arrival Form -->
                                <div id="cash-form" class="payment-form" style="display: none;">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-warning text-dark">
                                            <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Cash on Arrival</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="mb-3">Payment Details</h6>
                                                    <div class="cash-details">
                                                        <div class="detail-item mb-2">
                                                            <strong>Amount to Pay:</strong> ₱<?= number_format($booking['total_price'], 2) ?>
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Payment Location:</strong> Hotel Front Desk
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Payment Time:</strong> Upon Check-in
                                                        </div>
                                                        <div class="detail-item mb-2">
                                                            <strong>Booking Reference:</strong> BOOKING-<?= $booking_id ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6 class="mb-3">Important Notes</h6>
                                                    <ul class="payment-notes">
                                                        <li>Bring exact amount in cash</li>
                                                        <li>Payment must be made upon check-in</li>
                                                        <li>Present this booking confirmation</li>
                                                        <li>Valid ID required for verification</li>
                                                        <li>Late check-in may require advance notice</li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="alert alert-info mt-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Your booking is confirmed. Please pay the full amount in cash when you arrive at the hotel.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="pay-button" id="pay-btn" disabled>
                                <i class="fas fa-lock me-2"></i>Pay ₱<?= number_format($booking['total_price'], 2) ?> Securely
                            </button>
                        </form>

                        <!-- Security Information -->
                        <div class="security-info">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Secure Payment:</strong> Your payment information is encrypted and secure.
                            We use industry-standard SSL encryption to protect your data.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h5>Processing Payment...</h5>
        <p class="mb-0">Please wait while we process your payment securely.</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentCards = document.querySelectorAll('.payment-method-card');
    const payButton = document.getElementById('pay-btn');
    const paymentForm = document.getElementById('payment-form');
    const loadingOverlay = document.getElementById('loading-overlay');
    const paymentDetails = document.getElementById('payment-details');

    // Payment method form mapping
    const paymentForms = {
        'Credit/Debit Card': 'credit-card-form',
        'PayPal': 'paypal-form',
        'GCash': 'gcash-form',
        'Bank Transfer': 'bank-transfer-form',
        'Cash on Arrival': 'cash-form'
    };

    // Handle payment method selection
    paymentCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            paymentCards.forEach(c => c.classList.remove('selected'));

            // Add selected class to clicked card
            this.classList.add('selected');

            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;

            // Show payment details
            showPaymentForm(this.dataset.methodName);

            // Enable pay button
            payButton.disabled = false;
            updatePayButton(this.dataset.methodName);
        });
    });

    // Function to show appropriate payment form
    function showPaymentForm(methodName) {
        // Hide all payment forms
        document.querySelectorAll('.payment-form').forEach(form => {
            form.style.display = 'none';
        });

        // Show payment details container
        paymentDetails.style.display = 'block';

        // Show specific form
        const formId = paymentForms[methodName];
        if (formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.style.display = 'block';
            }
        }
    }

    // Function to update pay button text based on method
    function updatePayButton(methodName) {
        const amount = '<?= number_format($booking['total_price'], 2) ?>';
        let buttonText = '';
        let buttonIcon = 'fas fa-lock';

        switch(methodName) {
            case 'Credit/Debit Card':
                buttonText = `Charge Card ₱${amount}`;
                buttonIcon = 'fas fa-credit-card';
                break;
            case 'PayPal':
                buttonText = `Pay with PayPal ₱${amount}`;
                buttonIcon = 'fab fa-paypal';
                break;
            case 'GCash':
                buttonText = `Confirm GCash Payment ₱${amount}`;
                buttonIcon = 'fas fa-mobile-alt';
                break;
            case 'Bank Transfer':
                buttonText = `Confirm Bank Transfer ₱${amount}`;
                buttonIcon = 'fas fa-university';
                break;
            case 'Cash on Arrival':
                buttonText = `Confirm Booking ₱${amount}`;
                buttonIcon = 'fas fa-check';
                break;
            default:
                buttonText = `Pay ₱${amount} Securely`;
                buttonIcon = 'fas fa-lock';
        }

        payButton.innerHTML = `<i class="${buttonIcon} me-2"></i>${buttonText}`;
    }

    // Handle form submission
    paymentForm.addEventListener('submit', function(e) {
        const selectedMethod = document.querySelector('input[name="payment_method_id"]:checked');

        if (!selectedMethod) {
            e.preventDefault();
            alert('Please select a payment method.');
            return;
        }

        const methodName = document.querySelector(`[data-method="${selectedMethod.value}"]`).dataset.methodName;

        // Validate and copy values to hidden fields
        if (methodName === 'GCash') {
            const gcashRef = document.getElementById('gcash-reference').value.trim();
            if (!gcashRef) {
                e.preventDefault();
                alert('Please enter your GCash reference number.');
                document.getElementById('gcash-reference').focus();
                return;
            }
            document.getElementById('gcash_reference_hidden').value = gcashRef;
        }

        if (methodName === 'Bank Transfer') {
            const bankRef = document.getElementById('bank-reference').value.trim();
            if (!bankRef) {
                e.preventDefault();
                alert('Please enter your bank reference number.');
                document.getElementById('bank-reference').focus();
                return;
            }
            document.getElementById('bank_reference_hidden').value = bankRef;
        }

        if (methodName === 'Credit/Debit Card') {
            const cardNumber = document.getElementById('card-number').value.trim();
            const expiryDate = document.getElementById('expiry-date').value.trim();
            const cvv = document.getElementById('cvv').value.trim();

            if (!cardNumber || !expiryDate || !cvv) {
                e.preventDefault();
                alert('Please fill in all card details.');
                return;
            }
            document.getElementById('card_number_hidden').value = cardNumber;
        }

        // Show loading overlay
        loadingOverlay.style.display = 'flex';

        // Disable the pay button to prevent double submission
        payButton.disabled = true;
        payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });

    // Handle radio button changes
    document.querySelectorAll('input[name="payment_method_id"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                payButton.disabled = false;

                // Update card selection
                paymentCards.forEach(card => {
                    card.classList.remove('selected');
                    if (card.dataset.method === this.value) {
                        card.classList.add('selected');
                        showPaymentForm(card.dataset.methodName);
                        updatePayButton(card.dataset.methodName);
                    }
                });
            }
        });
    });

    // Card number formatting
    const cardNumberInput = document.getElementById('card-number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });
    }

    // Expiry date formatting
    const expiryInput = document.getElementById('expiry-date');
    if (expiryInput) {
        expiryInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });
    }

    // CVV formatting
    const cvvInput = document.getElementById('cvv');
    if (cvvInput) {
        cvvInput.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '');
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
