<?php
// User Payment Page for Booking
include 'includes/header.php';
include 'includes/db.php';
// Session is already started in header.php
$user_id = $_SESSION['user_id'] ?? 0;
$booking_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id || !$booking_id) {
    header('Location: my_bookings.php?msg=Invalid+request');
    exit;
}

// Get booking details with room type information
$stmt = $mysqli->prepare("
    SELECT b.*, r.room_number, h.hotel_name, h.phone as hotel_phone, h.address, h.city,
           rt.type_name, rt.description as room_description,
           u.first_name, u.last_name, u.email
    FROM bookings b
    JOIN rooms r ON b.room_id = r.room_id
    JOIN hotels h ON r.hotel_id = h.hotel_id
    JOIN room_types rt ON r.room_type_id = rt.room_type_id
    JOIN users u ON b.user_id = u.user_id
    WHERE b.booking_id = ? AND b.user_id = ?
    LIMIT 1
");
$stmt->bind_param('ii', $booking_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();
$booking = $result->fetch_assoc();
$stmt->close();

if (!$booking || $booking['payment_status'] == 'paid') {
    header('Location: my_bookings.php?msg=Invalid+or+already+paid');
    exit;
}

// Calculate nights
$checkin = new DateTime($booking['check_in_date']);
$checkout = new DateTime($booking['check_out_date']);
$nights = $checkin->diff($checkout)->days;

// Get payment methods
$methods = $mysqli->query("SELECT * FROM payment_methods WHERE is_active=1 ORDER BY payment_method_id");
?>

<style>
.payment-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.payment-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.payment-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.payment-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.payment-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 1;
}

.payment-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.booking-summary {
    background: #f8f9fc;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #4e73df;
}

.booking-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.booking-detail:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
    color: #1cc88a;
}

.booking-detail .label {
    color: #5a5c69;
    font-weight: 500;
}

.booking-detail .value {
    color: #2c3e50;
    font-weight: 600;
}

.payment-method-card {
    border: 2px solid #e3e6f0;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method-card:hover {
    border-color: #4e73df;
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.1);
    transform: translateY(-2px);
}

.payment-method-card.selected {
    border-color: #4e73df;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
}

.payment-method-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.payment-method-card:not(.selected) .payment-method-icon {
    background: #f8f9fc;
    color: #4e73df;
}

.payment-method-card.selected .payment-method-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.pay-button {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(28, 200, 138, 0.3);
}

.pay-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(28, 200, 138, 0.4);
}

.pay-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.security-info {
    background: #e8f5e8;
    border: 1px solid #d4edda;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1.5rem;
    text-align: center;
}

.security-info i {
    color: #1cc88a;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.breadcrumb-nav a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
}

.breadcrumb-nav a:hover {
    opacity: 1;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e73df;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .payment-container {
        padding: 1rem 0;
    }

    .payment-header h1 {
        font-size: 1.5rem;
    }

    .booking-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>

<div class="payment-container">
    <div class="container">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-nav">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="my_bookings.php"><i class="fas fa-calendar-check me-1"></i>My Bookings</a></li>
                    <li class="breadcrumb-item active text-white" aria-current="page">Payment</li>
                </ol>
            </nav>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8 col-xl-7">
                <div class="payment-card">
                    <!-- Payment Header -->
                    <div class="payment-header">
                        <h1><i class="fas fa-credit-card me-2"></i>Secure Payment</h1>
                        <p>Complete your booking payment for Booking #<?= $booking_id ?></p>
                    </div>

                    <div class="p-4">
                        <!-- Booking Summary -->
                        <div class="booking-summary">
                            <h4 class="mb-3"><i class="fas fa-file-invoice me-2 text-primary"></i>Booking Summary</h4>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-hotel me-2"></i>Hotel</span>
                                <span class="value"><?= htmlspecialchars($booking['hotel_name']) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-bed me-2"></i>Room</span>
                                <span class="value"><?= htmlspecialchars($booking['room_number']) ?> (<?= htmlspecialchars($booking['type_name']) ?>)</span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-calendar-alt me-2"></i>Check-in</span>
                                <span class="value"><?= date('M j, Y', strtotime($booking['check_in_date'])) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-calendar-alt me-2"></i>Check-out</span>
                                <span class="value"><?= date('M j, Y', strtotime($booking['check_out_date'])) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-moon me-2"></i>Duration</span>
                                <span class="value"><?= $nights ?> night<?= $nights > 1 ? 's' : '' ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-user me-2"></i>Guest</span>
                                <span class="value"><?= htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']) ?></span>
                            </div>

                            <div class="booking-detail">
                                <span class="label"><i class="fas fa-peso-sign me-2"></i>Total Amount</span>
                                <span class="value">₱<?= number_format($booking['total_price'], 2) ?></span>
                            </div>
                        </div>

                        <!-- Payment Form -->
                        <form id="payment-form" method="post" action="process_payment.php">
                            <input type="hidden" name="booking_id" value="<?= $booking_id ?>">

                            <h4 class="mb-3"><i class="fas fa-credit-card me-2 text-primary"></i>Select Payment Method</h4>

                            <div class="payment-methods mb-4">
                                <?php
                                $method_icons = [
                                    'Credit/Debit Card' => 'fas fa-credit-card',
                                    'PayPal' => 'fab fa-paypal',
                                    'GCash' => 'fas fa-mobile-alt'
                                ];

                                while($method = $methods->fetch_assoc()):
                                    $icon = $method_icons[$method['method_name']] ?? 'fas fa-money-bill';
                                ?>
                                <div class="payment-method-card" data-method="<?= $method['payment_method_id'] ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="payment-method-icon">
                                            <i class="<?= $icon ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?= htmlspecialchars($method['method_name']) ?></h6>
                                            <small class="opacity-75"><?= htmlspecialchars($method['description'] ?? 'Secure payment processing') ?></small>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method_id"
                                                   value="<?= $method['payment_method_id'] ?>" id="method_<?= $method['payment_method_id'] ?>" required>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>

                            <button type="submit" class="pay-button" id="pay-btn" disabled>
                                <i class="fas fa-lock me-2"></i>Pay ₱<?= number_format($booking['total_price'], 2) ?> Securely
                            </button>
                        </form>

                        <!-- Security Information -->
                        <div class="security-info">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Secure Payment:</strong> Your payment information is encrypted and secure.
                            We use industry-standard SSL encryption to protect your data.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h5>Processing Payment...</h5>
        <p class="mb-0">Please wait while we process your payment securely.</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentCards = document.querySelectorAll('.payment-method-card');
    const payButton = document.getElementById('pay-btn');
    const paymentForm = document.getElementById('payment-form');
    const loadingOverlay = document.getElementById('loading-overlay');

    // Handle payment method selection
    paymentCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            paymentCards.forEach(c => c.classList.remove('selected'));

            // Add selected class to clicked card
            this.classList.add('selected');

            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;

            // Enable pay button
            payButton.disabled = false;
        });
    });

    // Handle form submission
    paymentForm.addEventListener('submit', function(e) {
        const selectedMethod = document.querySelector('input[name="payment_method_id"]:checked');

        if (!selectedMethod) {
            e.preventDefault();
            alert('Please select a payment method.');
            return;
        }

        // Show loading overlay
        loadingOverlay.style.display = 'flex';

        // Disable the pay button to prevent double submission
        payButton.disabled = true;
        payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });

    // Handle radio button changes
    document.querySelectorAll('input[name="payment_method_id"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                payButton.disabled = false;

                // Update card selection
                paymentCards.forEach(card => {
                    card.classList.remove('selected');
                    if (card.dataset.method === this.value) {
                        card.classList.add('selected');
                    }
                });
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
