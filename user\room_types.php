<?php
// Room Types Display Page
include 'includes/session.php';
include 'includes/db.php';

// Include header after session checks
include 'includes/header.php';

// Display flash message if any
$flash = get_flash_message();
if ($flash) {
    echo "<div class='container mt-3'><div class='alert alert-{$flash['type']} alert-dismissible fade show' role='alert'>
            {$flash['message']}
            <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
          </div></div>";
}

// Define the 5 common room types
$roomTypes = [
    [
        'name' => 'Standard Room',
        'description' => 'Our comfortable standard rooms offer all the essentials for a pleasant stay. Each room features a comfortable bed, private bathroom, TV, and free Wi-Fi.',
        'image' => 'assets/images/rooms/standard.jpg',
        'price_range' => '₱1,500 - ₱3,000',
        'capacity' => '1-2',
        'amenities' => ['Free Wi-Fi', 'TV', 'Air Conditioning', 'Private Bathroom', 'Daily Housekeeping']
    ],
    [
        'name' => 'Deluxe Room',
        'description' => 'Our deluxe rooms offer additional space and upgraded amenities for a more comfortable stay. Enjoy premium bedding, a work desk, and enhanced bathroom facilities.',
        'image' => 'assets/images/rooms/deluxe.jpg',
        'price_range' => '₱3,000 - ₱5,000',
        'capacity' => '2-3',
        'amenities' => ['Free Wi-Fi', 'Smart TV', 'Air Conditioning', 'Mini Fridge', 'Work Desk', 'Premium Toiletries', 'Coffee Maker']
    ],
    [
        'name' => 'Suite',
        'description' => 'Our spacious suites feature separate living and sleeping areas, perfect for longer stays or those who desire more space. Enjoy premium amenities and elegant furnishings.',
        'image' => 'assets/images/rooms/suite.jpg',
        'price_range' => '₱5,000 - ₱8,000',
        'capacity' => '2-4',
        'amenities' => ['Free Wi-Fi', 'Smart TV', 'Separate Living Area', 'Mini Kitchen', 'Premium Bedding', 'Bathtub', 'Bathrobes', 'Room Service']
    ],
    [
        'name' => 'Family Room',
        'description' => 'Designed with families in mind, our family rooms offer ample space for everyone. Multiple beds, extra amenities, and thoughtful touches make these rooms perfect for family vacations.',
        'image' => 'assets/images/rooms/family.jpg',
        'price_range' => '₱4,000 - ₱7,000',
        'capacity' => '4-6',
        'amenities' => ['Free Wi-Fi', 'Smart TV', 'Multiple Beds', 'Extra Space', 'Family-friendly Amenities', 'Refrigerator', 'Microwave']
    ],
    [
        'name' => 'Executive Room',
        'description' => 'Our executive rooms cater to business travelers with dedicated work spaces, high-speed internet, and premium amenities. Enjoy comfort and productivity during your business trip.',
        'image' => 'assets/images/rooms/executive.jpg',
        'price_range' => '₱4,500 - ₱7,500',
        'capacity' => '1-2',
        'amenities' => ['High-speed Wi-Fi', 'Large Work Desk', 'Ergonomic Chair', 'Smart TV', 'Coffee Machine', 'Ironing Facilities', 'Express Check-in/out', 'Business Services']
    ]
];

// Get hotels for the dropdown
$hotels = $mysqli->query("SELECT hotel_id, hotel_name FROM hotels ORDER BY hotel_name");
?>

<!-- Room Types Hero Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="fw-bold mb-3">Our Room Types</h1>
                <p class="lead mb-4">Explore our standard room categories available across all our hotels. Each room type offers a unique experience tailored to different needs and preferences.</p>
            </div>
            <div class="col-lg-6 d-none d-lg-block">
                <img src="assets/images/rooms/room-types-hero.jpg" alt="Room Types" class="img-fluid rounded-3 shadow" style="max-height: 300px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>

<!-- Hotel Selection Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="card border-0 shadow">
            <div class="card-body p-4">
                <h4 class="card-title mb-4"><i class="fas fa-hotel text-primary me-2"></i>Select a Hotel</h4>
                <form class="row g-3" method="get" action="room.php">
                    <div class="col-md-8">
                        <label for="hotel_id" class="form-label">Hotel</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <select class="form-select" id="hotel_id" name="hotel_id">
                                <option value="">All Hotels</option>
                                <?php while($hotel = $hotels->fetch_assoc()): ?>
                                    <option value="<?= $hotel['hotel_id'] ?>"><?= htmlspecialchars($hotel['hotel_name']) ?></option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" type="submit">
                            <i class="fa fa-search me-2"></i> View Available Rooms
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Room Types Section -->
<section class="py-5">
    <div class="container">
        <h2 class="fw-bold mb-4 text-center">Our Standard Room Categories</h2>
        <p class="text-muted text-center mb-5">These room types are available across all our hotels, with slight variations in design and amenities depending on the specific hotel.</p>
        
        <div class="row g-4">
            <?php foreach($roomTypes as $index => $room): ?>
            <div class="col-md-6 col-lg-4">
                <div class="card room-type-card h-100 border-0 shadow-sm">
                    <img src="<?= $room['image'] ?>" class="card-img-top" alt="<?= htmlspecialchars($room['name']) ?>" style="height: 200px; object-fit: cover;">
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($room['name']) ?></h5>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="badge bg-info text-dark">
                                <i class="fas fa-users me-1"></i> <?= htmlspecialchars($room['capacity']) ?> Guests
                            </span>
                            <span class="badge bg-success">
                                <i class="fas fa-tag me-1"></i> <?= htmlspecialchars($room['price_range']) ?>
                            </span>
                        </div>
                        <p class="card-text"><?= htmlspecialchars($room['description']) ?></p>
                        
                        <h6 class="mt-3 mb-2">Amenities:</h6>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <?php foreach($room['amenities'] as $amenity): ?>
                            <span class="badge bg-light text-dark"><?= htmlspecialchars($amenity) ?></span>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="d-grid mt-3">
                            <a href="room.php?type=<?= urlencode($room['name']) ?>" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>Find Available Rooms
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-light">
    <div class="container text-center">
        <h3 class="fw-bold mb-3">Ready to Experience Our Accommodations?</h3>
        <p class="mb-4">Book your stay now and enjoy our comfortable rooms and exceptional service.</p>
        <a href="hotels.php" class="btn btn-primary btn-lg">
            <i class="fas fa-hotel me-2"></i>Browse Our Hotels
        </a>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
