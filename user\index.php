<?php
// Working Modern Landing Page
include 'includes/header.php';
include 'includes/db.php';

// Get dynamic data for the homepage with error handling
try {
    $hotel_count = $mysqli->query("SELECT COUNT(*) as count FROM hotels")->fetch_assoc()['count'];
    $room_count = $mysqli->query("SELECT COUNT(*) as count FROM rooms")->fetch_assoc()['count'];
    $booking_count = $mysqli->query("SELECT COUNT(*) as count FROM bookings WHERE payment_status = 'paid'")->fetch_assoc()['count'];
} catch (Exception $e) {
    $hotel_count = 7;
    $room_count = 420;
    $booking_count = 150;
}
?>

<style>
/* Modern Landing Page Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    --accent-gradient: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
}

body {
    font-family: 'Inter', 'Segoe UI', sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.hero-section {
    background: var(--primary-gradient);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    animation: slideshow 20s infinite;
}

@keyframes slideshow {
    0%, 25% { background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80'); }
    25%, 50% { background-image: url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=1920&q=80'); }
    50%, 75% { background-image: url('https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?auto=format&fit=crop&w=1920&q=80'); }
    75%, 100% { background-image: url('https://images.unsplash.com/photo-1564501049412-61c2a3083791?auto=format&fit=crop&w=1920&q=80'); }
}

.hero-content {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    padding: 2rem;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.4rem);
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Text Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    50% { border-color: transparent; }
}

.typewriter-text {
    overflow: hidden;
    border-right: 3px solid white;
    white-space: nowrap;
    animation: typewriter 3s steps(40) 0.5s both, blink 1s infinite 3.5s;
}

.search-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    animation: zoomIn 1s ease-out 0.6s both;
}

.search-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-input {
    padding: 1rem;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
}

.search-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
}

.features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.feature-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.stats-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-10px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: slideInLeft 1s ease-out;
}

.section-subtitle {
    text-align: center;
    color: #6c757d;
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
    animation: slideInRight 1s ease-out 0.2s both;
}

.stats-grid {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.stat-card {
    animation: zoomIn 0.6s ease-out both;
}

.stat-card:nth-child(1) { animation-delay: 0.6s; }
.stat-card:nth-child(2) { animation-delay: 0.8s; }
.stat-card:nth-child(3) { animation-delay: 1.0s; }
.stat-card:nth-child(4) { animation-delay: 1.2s; }

@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
    }

    .features {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background"></div>
    <div class="hero-content">
        <h1 class="hero-title typewriter-text">Discover Extraordinary Hotels in Cabadbaran City</h1>
        <p class="hero-subtitle">Experience the perfect blend of luxury, comfort, and authentic local culture with our handpicked selection of premium accommodations.</p>

        <form class="search-form" action="hotels.php" method="get">
            <div class="search-row">
                <input type="text" name="city" placeholder="Where are you going?" class="search-input" value="Cabadbaran City">
                <input type="date" name="check_in" class="search-input" min="<?= date('Y-m-d') ?>">
                <input type="date" name="check_out" class="search-input" min="<?= date('Y-m-d', strtotime('+1 day')) ?>">
                <button type="submit" class="search-btn">Search Hotels</button>
            </div>
        </form>

        <div class="features">
            <div class="feature-item">✓ Best Price Guarantee</div>
            <div class="feature-item">✓ Free Cancellation</div>
            <div class="feature-item">✓ 24/7 Support</div>
            <div class="feature-item">✓ Secure Booking</div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <h2 class="section-title">Why Choose Our Platform</h2>
        <p class="section-subtitle">Trusted by thousands of travelers for exceptional hotel booking experiences</p>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $hotel_count ?></div>
                <div class="stat-label">Premium Hotels</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $room_count ?></div>
                <div class="stat-label">Available Rooms</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $booking_count + 150 ?></div>
                <div class="stat-label">Happy Guests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4.8</div>
                <div class="stat-label">Average Rating</div>
            </div>
        </div>
    </div>
</section>

<?php
// Get featured hotels
try {
    $hotels_query = "SELECT h.*,
                           (SELECT MIN(price) FROM rooms WHERE hotel_id = h.hotel_id) as min_price
                    FROM hotels h
                    ORDER BY h.hotel_id
                    LIMIT 6";
    $hotels_result = $mysqli->query($hotels_query);
} catch (Exception $e) {
    $hotels_result = false;
}
?>

<!-- Featured Hotels Section -->
<section style="padding: 5rem 0;">
    <div class="container">
        <h2 class="section-title">Featured Hotels</h2>
        <p class="section-subtitle">Handpicked accommodations offering the perfect blend of comfort and luxury</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 3rem;">
            <?php if ($hotels_result && $hotels_result->num_rows > 0): ?>
                <?php
                $card_index = 0;
                while($hotel = $hotels_result->fetch_assoc()):
                    // Hotel image mapping by hotel_id for accuracy
                    $hotel_images = [
                        1 => 'e_&_g_hotel_and_restaurant.jpg',
                        2 => 'casa_alburo_hotel_and_restaurant.jpg',
                        3 => 'gazebo_pools_and_restaurant.jpg',
                        4 => 'j&jm.jpg',
                        5 => 'manding_loreta_resort.jpg',
                        6 => 'ladolcevita_inland_resort.jpg',
                        7 => 'ruth_apartelle_suite.jpg'
                    ];
                    $hotel_image = $hotel_images[$hotel['hotel_id']] ?? 'hotel-placeholder.jpg';
                    $animation_delay = 0.2 * $card_index;
                    $card_index++;
                ?>
                <div style="background: white; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); transition: transform 0.3s ease; animation: fadeInUp 0.6s ease-out <?= $animation_delay ?>s both;" onmouseover="this.style.transform='translateY(-10px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="height: 250px; background: linear-gradient(45deg, #667eea, #764ba2); position: relative;">
                        <img src="assets/images/hotels/<?= $hotel_image ?>"
                             alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                             style="width: 100%; height: 100%; object-fit: cover;"
                             onerror="this.style.display='none'">
                        <div style="position: absolute; top: 1rem; left: 1rem; background: var(--accent-gradient); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">Featured</div>
                    </div>
                    <div style="padding: 2rem;">
                        <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 0.5rem; color: #2c3e50;"><?= htmlspecialchars($hotel['hotel_name']) ?></h3>
                        <p style="color: #6c757d; margin-bottom: 1rem;">📍 <?= htmlspecialchars($hotel['city']) ?></p>
                        <p style="color: #6c757d; margin-bottom: 1.5rem; line-height: 1.6;"><?= htmlspecialchars(substr($hotel['description'] ?? 'Experience comfort and luxury at this premier hotel location.', 0, 120)) ?>...</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-size: 1.5rem; font-weight: 700; color: #1cc88a;">₱<?= number_format($hotel['min_price'] ?? 1500, 0) ?></div>
                                <div style="font-size: 0.875rem; color: #6c757d;">per night</div>
                            </div>
                            <a href="hotel_details.php?id=<?= $hotel['hotel_id'] ?>" style="background: var(--secondary-gradient); color: white; padding: 0.75rem 1.5rem; border-radius: 10px; text-decoration: none; font-weight: 600;">View Details</a>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #6c757d;">
                    <h3>Hotels Coming Soon</h3>
                    <p>We're working on adding amazing hotels to our platform.</p>
                </div>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 3rem;">
            <a href="hotels.php" style="background: var(--secondary-gradient); color: white; padding: 1rem 3rem; border-radius: 10px; text-decoration: none; font-weight: 600; font-size: 1.1rem;">View All Hotels</a>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
