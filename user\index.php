<?php
// Modern User Home/Landing Page
include 'includes/header.php';
include 'includes/db.php';
?>

<!-- Hero Section with Background Image -->
<section class="hero-section">
    <img src="assets/images/hotel-placeholder.jpg" alt="Hotel Background" class="hero-background">
    <div class="hero-content">
        <h1 class="hero-title">Welcome to the Future of Hotel Booking</h1>
        <p class="hero-subtitle">Discover smart rooms, sustainable stays, and exclusive innovations. Book your next experience with confidence and style.</p>
        <div class="d-flex justify-content-center gap-3">
            <a href="hotels.php" class="btn btn-primary btn-lg">Find Your Hotel</a>
            <a href="room.php" class="btn btn-outline-light btn-lg">Browse Rooms</a>
        </div>
    </div>
</section>

<!-- Search Bar Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="card border-0 shadow">
            <div class="card-body p-4">
                <form action="hotels.php" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="destination" class="form-label">Destination</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="destination" name="city" placeholder="Where are you going?">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="check-in" class="form-label">Check In</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control datepicker" id="check-in" placeholder="Select date">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="check-out" class="form-label">Check Out</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control datepicker" id="check-out" placeholder="Select date">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Why Book With Us Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Why Book With Us?</h2>
            <p class="text-muted">Experience the difference with our innovative hotel booking platform</p>
        </div>
        <div class="row g-4 justify-content-center">
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-microchip"></i>
                    </div>
                    <h5>Smart Room Innovations</h5>
                    <p class="text-muted">Control your room with your voice, enjoy digital concierge, and experience the latest in hotel technology.</p>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-leaf"></i>
                    </div>
                    <h5>Sustainable Stays</h5>
                    <p class="text-muted">Book eco-friendly hotels with verified sustainability initiatives and make a positive impact with every stay.</p>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-star"></i>
                    </div>
                    <h5>Personalized Experience</h5>
                    <p class="text-muted">Get recommendations tailored to your preferences and enjoy seamless, real-time booking and support.</p>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-danger text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-shield-alt"></i>
                    </div>
                    <h5>Secure Booking</h5>
                    <p class="text-muted">Book with confidence knowing your personal and payment information is protected with advanced security.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Hotels Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-0">Featured Hotels</h2>
                <p class="text-muted">Discover our handpicked selection of exceptional hotels</p>
            </div>
            <a href="hotels.php" class="btn btn-outline-primary">View All Hotels</a>
        </div>

        <div class="row g-4">
            <?php
            $hotels = $mysqli->query("SELECT h.*,
                                      (SELECT image_url FROM hotel_images WHERE hotel_id = h.hotel_id LIMIT 1) as image_url
                                      FROM hotels h
                                      JOIN hotel_categories c ON h.category_id = c.category_id
                                      ORDER BY h.star_rating DESC, h.hotel_name LIMIT 6");

            while($hotel = $hotels->fetch_assoc()):
                // Get the correct image path based on hotel name
                $hotel_image = 'assets/images/hotel-placeholder.jpg';
                $hotel_name_clean = strtolower(str_replace(' ', '_', $hotel['hotel_name']));

                // Check for matching hotel images
                foreach(['casa_alburo', 'eg_hotel', 'gazebo_pools', 'hotel5', 'j_jm_hotel', 'ladolcevita_resort', 'manding_loreta', 'Ruth_Apartelle_Suite'] as $img_name) {
                    if(strpos($img_name, $hotel_name_clean) !== false || strpos($hotel_name_clean, $img_name) !== false) {
                        $hotel_image = "assets/images/hotels/{$img_name}.jpg";
                        break;
                    }
                }
            ?>
            <div class="col-md-6 col-lg-4">
                <div class="card hotel-card border-0 h-100">
                    <div class="position-relative">
                        <img src="<?= $hotel_image ?>" class="card-img-top" alt="<?= htmlspecialchars($hotel['hotel_name']) ?>">
                        <div class="hotel-rating">
                            <i class="fas fa-star"></i> <?= intval($hotel['star_rating']) ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($hotel['hotel_name']) ?></h5>
                        <div class="hotel-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?= htmlspecialchars($hotel['city']) ?></span>
                        </div>
                        <p class="card-text"><?= htmlspecialchars(substr($hotel['description'], 0, 100)) ?>...</p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="hotel-price">
                                From ₱<?= number_format($hotel['price_range_from'], 2) ?>
                            </div>
                            <a href="hotel_details.php?id=<?= $hotel['hotel_id'] ?>" class="btn btn-sm btn-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">What Our Guests Say</h2>
            <p class="text-muted">Read testimonials from our satisfied customers</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <?php
                        $testimonials = $mysqli->query("SELECT r.*, u.first_name, u.last_name, h.hotel_name
                                                      FROM reviews r
                                                      JOIN bookings b ON r.booking_id = b.booking_id
                                                      JOIN users u ON b.user_id = u.user_id
                                                      JOIN rooms rm ON b.room_id = rm.room_id
                                                      JOIN hotels h ON rm.hotel_id = h.hotel_id
                                                      WHERE r.rating >= 4
                                                      ORDER BY r.created_at DESC LIMIT 5");

                        $active = true;
                        while($testimonial = $testimonials->fetch_assoc()):
                        ?>
                        <div class="carousel-item <?= $active ? 'active' : '' ?>">
                            <div class="card border-0 shadow p-4">
                                <div class="d-flex justify-content-between mb-3">
                                    <div>
                                        <h5 class="mb-0"><?= htmlspecialchars($testimonial['first_name'] . ' ' . substr($testimonial['last_name'], 0, 1) . '.') ?></h5>
                                        <p class="text-muted mb-0">Stayed at <?= htmlspecialchars($testimonial['hotel_name']) ?></p>
                                    </div>
                                    <div class="text-warning">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?= $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted' ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                <p class="mb-0">"<?= htmlspecialchars($testimonial['comment']) ?>"</p>
                            </div>
                        </div>
                        <?php
                        $active = false;
                        endwhile;

                        // If no testimonials, show a default one
                        if($testimonials->num_rows == 0):
                        ?>
                        <div class="carousel-item active">
                            <div class="card border-0 shadow p-4">
                                <div class="d-flex justify-content-between mb-3">
                                    <div>
                                        <h5 class="mb-0">John D.</h5>
                                        <p class="text-muted mb-0">Stayed at Gazebo Pools Resort</p>
                                    </div>
                                    <div class="text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <p class="mb-0">"Amazing experience! The smart room features were incredible and the staff was very attentive. Will definitely book again on my next trip."</p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon bg-primary rounded-circle" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon bg-primary rounded-circle" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-primary text-white text-center">
    <div class="container">
        <h2 class="fw-bold mb-3">Ready to Experience the Future of Hotel Booking?</h2>
        <p class="mb-4">Join thousands of satisfied customers who have transformed their travel experience with SmartHotel.</p>
        <div class="d-flex justify-content-center gap-3">
            <a href="hotels.php" class="btn btn-light btn-lg">Find Hotels</a>
            <a href="register.php" class="btn btn-outline-light btn-lg">Sign Up Now</a>
        </div>
    </div>
</section>

<style>
.feature-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.carousel-control-prev-icon, .carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    padding: 1.5rem;
}
</style>

<?php include 'includes/footer.php'; ?>
