<?php
// Working Modern Landing Page
include 'includes/header.php';
include 'includes/db.php';

// Get dynamic data for the homepage with error handling
try {
    $hotel_count = $mysqli->query("SELECT COUNT(*) as count FROM hotels")->fetch_assoc()['count'];
    $room_count = $mysqli->query("SELECT COUNT(*) as count FROM rooms")->fetch_assoc()['count'];
    $booking_count = $mysqli->query("SELECT COUNT(*) as count FROM bookings WHERE payment_status = 'paid'")->fetch_assoc()['count'];
} catch (Exception $e) {
    $hotel_count = 7;
    $room_count = 420;
    $booking_count = 150;
}
?>

<!-- Modern Typography -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

<style>
/* Ultra-Modern UI Design System */
:root {
    /* Modern Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    --accent-gradient: linear-gradient(135deg, #00d4aa 0%, #00a085 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    /* Neumorphism */
    --neu-light: #ffffff;
    --neu-dark: #d1d9e6;
    --neu-shadow-light: 20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff;
    --neu-shadow-inset: inset 20px 20px 60px #d1d9e6, inset -20px -20px 60px #ffffff;

    /* Modern Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Poppins', 'Inter', sans-serif;

    /* Spacing & Sizing */
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: #2c3e50;
    overflow-x: hidden;
    scroll-behavior: smooth;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Modern Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    overflow: hidden;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #00d4aa 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.18;
    animation: slideshow 25s infinite, parallaxFloat 20s ease-in-out infinite;
}

@keyframes slideshow {
    0%, 20% { background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80'); }
    25%, 45% { background-image: url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=1920&q=80'); }
    50%, 70% { background-image: url('https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?auto=format&fit=crop&w=1920&q=80'); }
    75%, 95% { background-image: url('https://images.unsplash.com/photo-1564501049412-61c2a3083791?auto=format&fit=crop&w=1920&q=80'); }
    100% { background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80'); }
}

@keyframes parallaxFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-20px) scale(1.02); }
}

/* Floating Geometric Shapes */
.hero-section::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 100px;
    height: 100px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 15%;
    width: 80px;
    height: 80px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    animation: float 8s ease-in-out infinite reverse;
    z-index: 1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

/* Modern Hero Content */
.hero-content {
    position: relative;
    z-index: 2;
    background: rgba(255,255,255,0.97);
    box-shadow: 0 8px 32px rgba(0,0,0,0.10);
    padding: 3.5rem 2.5rem;
    border-radius: 2rem;
    max-width: 420px;
    margin: 0 auto;
    text-align: center;
}

/* Modern Typography */
.hero-title {
    font-family: var(--font-display);
    font-size: 2.2rem;
    font-weight: 800;
    line-height: 1.1;
    color: #2c3e50;
    text-shadow: none;
    background: none;
    -webkit-text-fill-color: #2c3e50;
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}

.hero-subtitle {
    font-size: 1.1rem;
    color: #4e73df;
    margin-top: 1.5rem;
    margin-bottom: 2.5rem;
    font-weight: 500;
    text-shadow: none;
}

/* Text Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}



/* Ultra-Modern Search Form */
.search-form {
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    margin-bottom: 3rem;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0;
    transform: scale(0.95);
    transition: var(--transition);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
}

.search-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.8s ease;
}

.search-form:hover::before {
    left: 100%;
}

.search-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
    align-items: end;
}

.search-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-btn-field {
    align-self: end;
}

/* Modern Input Fields */
.search-input {
    padding: 1.2rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    font-size: 1rem;
    font-weight: 500;
    color: #2c3e50;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-gradient);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 170, 0.2);
}

.search-input::placeholder {
    color: #6c757d;
    font-weight: 400;
}

/* Ultra-Modern Search Button */
.search-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 1.2rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 56px;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.search-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 212, 170, 0.4);
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn:active {
    transform: translateY(-1px);
}

.search-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

.search-btn.loading {
    pointer-events: none;
}

.search-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modern Feature Badges */
.features {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
    position: relative;
    z-index: 2;
}

.feature-item {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    padding: 1rem 2rem;
    border-radius: 50px;
    backdrop-filter: blur(15px);
    transition: var(--transition);
    font-weight: 500;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.95);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-item:hover::before {
    left: 100%;
}

/* Ultra-Modern Stats Section */
.stats-section {
    padding: 8rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: rotate 30s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

/* Modern Grid Layout */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

/* Neumorphism Stat Cards */
.stat-card {
    background: var(--neu-light);
    padding: 3rem 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--neu-shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--accent-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stat-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Modern Stat Typography */
.stat-number {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 4vw, 3.5rem);
    font-weight: 800;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    line-height: 1;
    letter-spacing: -0.02em;
}

.stat-label {
    color: #5a6c7d;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Modern Section Headers */
.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    text-align: center;
    margin-bottom: 1.5rem;
    background: var(--dark-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.1;
    animation: slideInLeft 1s ease-out;
}

.section-subtitle {
    text-align: center;
    color: #5a6c7d;
    font-size: clamp(1.1rem, 2vw, 1.4rem);
    max-width: 700px;
    margin: 0 auto;
    font-weight: 400;
    line-height: 1.6;
    animation: slideInRight 1s ease-out 0.2s both;
}

.stats-grid {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.stat-card {
    animation: zoomIn 0.6s ease-out both;
}

.stat-card:nth-child(1) { animation-delay: 0.6s; }
.stat-card:nth-child(2) { animation-delay: 0.8s; }
.stat-card:nth-child(3) { animation-delay: 1.0s; }
.stat-card:nth-child(4) { animation-delay: 1.2s; }

/* Ultra-Modern Hotel Cards */
.modern-hotel-card {
    background: var(--neu-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--neu-shadow-light);
    transition: var(--transition);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.modern-hotel-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.hotel-image-container {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: var(--primary-gradient);
}

.hotel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.modern-hotel-card:hover .hotel-image {
    transform: scale(1.1);
}

.hotel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.2) 100%);
    opacity: 0;
    transition: var(--transition);
}

.modern-hotel-card:hover .hotel-overlay {
    opacity: 1;
}

.featured-badge {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    background: var(--accent-gradient);
    color: white;
    padding: 0.7rem 1.2rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
    backdrop-filter: blur(10px);
}

.hotel-rating-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.7rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    backdrop-filter: blur(10px);
}

.hotel-rating-badge i {
    color: #ffd700;
}

/* Modern Hotel Content */
.hotel-content {
    padding: 2.5rem;
}

.hotel-title {
    font-family: var(--font-display);
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
    line-height: 1.3;
}

.hotel-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #5a6c7d;
    font-weight: 500;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.hotel-location i {
    color: var(--accent-gradient);
    font-size: 0.9rem;
}

.hotel-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.hotel-amenities {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.amenity-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #4e73df;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.amenity-tag i {
    font-size: 0.75rem;
}

.hotel-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.price-section {
    display: flex;
    flex-direction: column;
}

.price {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 800;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.price-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.modern-btn {
    background: var(--secondary-gradient);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.3);
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover i {
    transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .search-row {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .search-btn-field {
        grid-column: 1 / -1;
        justify-self: center;
    }

    .search-btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .search-btn-field {
        grid-column: 1;
    }

    .features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .hero-content {
        padding: 2rem 1rem;
    }

    .search-form {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .hotel-content {
        padding: 2rem 1.5rem;
    }

    .hotel-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .modern-btn {
        justify-content: center;
    }
}

/* Professional Hero Section Styles */
.hero-section-new {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #00d4aa 100%);
    padding: 2rem 1rem;
}

.hero-background-new {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-overlay-new {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.floating-elements-new {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.floating-shape-new {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatNew 8s ease-in-out infinite;
}

.shape-1 {
    width: 120px;
    height: 120px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes floatNew {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.hero-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    position: relative;
    z-index: 10;
}

.hero-content-new {
    text-align: center;
    color: white;
    animation: fadeInUp 1s ease-out;
}

.hero-badge-new {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    animation: slideInDown 1s ease-out 0.2s both;
}

.hero-badge-new i {
    color: #ffd700;
}

.hero-title-new {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.title-line-1 {
    display: block;
    animation: slideInLeft 1s ease-out 0.4s both;
}

.title-line-2 {
    display: block;
    background: linear-gradient(45deg, #00d4aa, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInRight 1s ease-out 0.6s both;
}

.hero-subtitle-new {
    font-size: clamp(1.1rem, 2vw, 1.4rem);
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.95;
    animation: fadeIn 1s ease-out 0.8s both;
}

/* Professional Search Container */
.search-container-new {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    animation: zoomIn 1s ease-out 1s both;
}

.search-header-new {
    text-align: center;
    margin-bottom: 2rem;
}

.search-header-new h3 {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.search-header-new p {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

.search-grid-new {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.search-field-new {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.search-label-new {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #4e73df;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-label-new i {
    font-size: 0.8rem;
    color: #00d4aa;
}

.search-input-new {
    padding: 1rem 1.25rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    color: #2c3e50;
    background: white;
    transition: all 0.3s ease;
    outline: none;
}

.search-input-new:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
    transform: translateY(-2px);
}

.search-input-new::placeholder {
    color: #adb5bd;
    font-weight: 400;
}

.search-btn-new {
    background: linear-gradient(135deg, #4e73df 0%, #00d4aa 100%);
    color: white;
    border: none;
    border-radius: 16px;
    padding: 1.25rem 2rem;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-btn-new:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(78, 115, 223, 0.4);
}

.search-btn-new:active {
    transform: translateY(-1px);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.search-btn-new:hover .btn-shine {
    left: 100%;
}

/* Trust Indicators */
.trust-indicators-new {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 1.2s both;
}

.trust-item-new {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.trust-item-new:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.trust-item-new i {
    color: #00d4aa;
    font-size: 1rem;
}

/* Responsive Design for New Hero */
@media (max-width: 992px) {
    .search-grid-new {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .search-container-new {
        padding: 2rem;
    }

    .trust-indicators-new {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .search-grid-new {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .search-container-new {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .trust-indicators-new {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .hero-section-new {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .search-header-new h3 {
        font-size: 1.5rem;
    }

    .search-btn-new {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .trust-item-new {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}
</style>

<!-- Professional Hero Section -->
<section class="hero-section-new">
    <div class="hero-background-new">
        <div class="hero-overlay-new"></div>
        <div class="floating-elements-new">
            <div class="floating-shape-new shape-1"></div>
            <div class="floating-shape-new shape-2"></div>
            <div class="floating-shape-new shape-3"></div>
        </div>
    </div>

    <div class="hero-container">
        <div class="hero-content-new">
            <div class="hero-badge-new">
                <i class="fas fa-star"></i>
                <span>Premium Hotel Booking Platform</span>
            </div>

            <h1 class="hero-title-new">
                <span class="title-line-1">Discover Extraordinary</span>
                <span class="title-line-2">Hotels in Cabadbaran City</span>
            </h1>

            <p class="hero-subtitle-new">Experience the best of Cabadbaran City. Modern hotels, local warmth, and seamless booking—your next stay starts here.</p>

            <div class="search-container-new">
                <form class="search-form-new" action="hotels.php" method="get">
                    <div class="search-header-new">
                        <h3>Find Your Perfect Stay</h3>
                        <p>Search from our curated collection of premium hotels</p>
                    </div>

                    <div class="search-grid-new">
                        <div class="search-field-new destination-field">
                            <label class="search-label-new">
                                <i class="fas fa-map-marker-alt"></i>
                                Destination
                            </label>
                            <input type="text" name="city" placeholder="Cabadbaran City" class="search-input-new" value="Cabadbaran City">
                        </div>

                        <div class="search-field-new">
                            <label class="search-label-new">
                                <i class="fas fa-calendar-alt"></i>
                                Check-in
                            </label>
                            <input type="date" name="check_in" class="search-input-new" min="<?= date('Y-m-d') ?>" value="<?= date('Y-m-d') ?>">
                        </div>

                        <div class="search-field-new">
                            <label class="search-label-new">
                                <i class="fas fa-calendar-alt"></i>
                                Check-out
                            </label>
                            <input type="date" name="check_out" class="search-input-new" min="<?= date('Y-m-d', strtotime('+1 day')) ?>" value="<?= date('Y-m-d', strtotime('+2 days')) ?>">
                        </div>

                        <div class="search-field-new guests-field">
                            <label class="search-label-new">
                                <i class="fas fa-users"></i>
                                Guests
                            </label>
                            <select name="guests" class="search-input-new">
                                <option value="1">1 Guest</option>
                                <option value="2" selected>2 Guests</option>
                                <option value="3">3 Guests</option>
                                <option value="4">4 Guests</option>
                                <option value="5+">5+ Guests</option>
                            </select>
                        </div>
                    </div>

                    <button type="submit" class="search-btn-new">
                        <span class="btn-text">Search Hotels</span>
                        <i class="fas fa-search"></i>
                        <div class="btn-shine"></div>
                    </button>
                </form>
            </div>

            <div class="trust-indicators-new">
                <div class="trust-item-new">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Booking</span>
                </div>
                <div class="trust-item-new">
                    <i class="fas fa-medal"></i>
                    <span>Best Price Guarantee</span>
                </div>
                <div class="trust-item-new">
                    <i class="fas fa-clock"></i>
                    <span>24/7 Support</span>
                </div>
                <div class="trust-item-new">
                    <i class="fas fa-undo"></i>
                    <span>Free Cancellation</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <h2 class="section-title">Why Choose Our Platform</h2>
        <p class="section-subtitle">Trusted by thousands of travelers for exceptional hotel booking experiences</p>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $hotel_count ?></div>
                <div class="stat-label">Premium Hotels</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $room_count ?></div>
                <div class="stat-label">Available Rooms</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $booking_count + 150 ?></div>
                <div class="stat-label">Happy Guests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4.8</div>
                <div class="stat-label">Average Rating</div>
            </div>
        </div>
    </div>
</section>

<?php
// Get featured hotels
try {
    $hotels_query = "SELECT h.*,
                           (SELECT MIN(price) FROM rooms WHERE hotel_id = h.hotel_id) as min_price
                    FROM hotels h
                    ORDER BY h.hotel_id
                    LIMIT 6";
    $hotels_result = $mysqli->query($hotels_query);
} catch (Exception $e) {
    $hotels_result = false;
}
?>

<!-- Featured Hotels Section -->
<section style="padding: 5rem 0;">
    <div class="container">
        <h2 class="section-title">Featured Hotels</h2>
        <p class="section-subtitle">Handpicked accommodations offering the perfect blend of comfort and luxury</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 3rem;">
            <?php if ($hotels_result && $hotels_result->num_rows > 0): ?>
                <?php
                $card_index = 0;
                while($hotel = $hotels_result->fetch_assoc()):
                    // Hotel image mapping by hotel_id for accuracy
                    $hotel_images = [
                        1 => 'e_&_g_hotel_and_restaurant.jpg',
                        2 => 'casa_alburo_hotel_and_restaurant.jpg',
                        3 => 'gazebo_pools_and_restaurant.jpg',
                        4 => 'j&jm.jpg',
                        5 => 'manding_loreta_resort.jpg',
                        6 => 'ladolcevita_inland_resort.jpg',
                        7 => 'ruth_apartelle_suite.jpg'
                    ];
                    $hotel_image = $hotel_images[$hotel['hotel_id']] ?? 'hotel-placeholder.jpg';
                    $animation_delay = 0.2 * $card_index;
                    $card_index++;
                ?>
                <div class="modern-hotel-card" style="animation: fadeInUp 0.6s ease-out <?= $animation_delay ?>s both;">
                    <div class="hotel-image-container">
                        <img src="assets/images/hotels/<?= $hotel_image ?>"
                             alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                             class="hotel-image"
                             onerror="this.style.display='none'">
                        <div class="hotel-overlay"></div>
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                            Featured
                        </div>
                        <div class="hotel-rating-badge">
                            <i class="fas fa-star"></i>
                            4.8
                        </div>
                    </div>
                    <div class="hotel-content">
                        <h3 class="hotel-title"><?= htmlspecialchars($hotel['hotel_name']) ?></h3>
                        <div class="hotel-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <?= htmlspecialchars($hotel['city']) ?>
                        </div>
                        <p class="hotel-description"><?= htmlspecialchars(substr($hotel['description'] ?? 'Experience comfort and luxury at this premier hotel location.', 0, 120)) ?>...</p>

                        <div class="hotel-amenities">
                            <span class="amenity-tag"><i class="fas fa-wifi"></i> Free WiFi</span>
                            <span class="amenity-tag"><i class="fas fa-swimming-pool"></i> Pool</span>
                            <span class="amenity-tag"><i class="fas fa-utensils"></i> Restaurant</span>
                        </div>

                        <div class="hotel-footer">
                            <div class="price-section">
                                <div class="price">₱<?= number_format($hotel['min_price'] ?? 1500, 0) ?></div>
                                <div class="price-label">per night</div>
                            </div>
                            <a href="hotel_details.php?id=<?= $hotel['hotel_id'] ?>" class="modern-btn">
                                <span>View Details</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #6c757d;">
                    <h3>Hotels Coming Soon</h3>
                    <p>We're working on adding amazing hotels to our platform.</p>
                </div>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 3rem;">
            <a href="hotels.php" style="background: var(--secondary-gradient); color: white; padding: 1rem 3rem; border-radius: 10px; text-decoration: none; font-weight: 600; font-size: 1.1rem;">View All Hotels</a>
        </div>
    </div>
</section>

<script>
// Professional Hero Animations and Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form handling with loading states
    const searchForm = document.querySelector('.search-form-new');
    const searchBtn = document.querySelector('.search-btn-new');

    if (searchForm && searchBtn) {
        searchForm.addEventListener('submit', function(e) {
            // Add loading state
            searchBtn.classList.add('loading');
            searchBtn.disabled = true;

            // Update button text
            const btnText = searchBtn.querySelector('.btn-text');
            if (btnText) {
                btnText.textContent = 'Searching...';
            }

            // Set default dates if not provided
            const checkinInput = document.querySelector('input[name="check_in"]');
            const checkoutInput = document.querySelector('input[name="check_out"]');

            if (checkinInput && !checkinInput.value) {
                checkinInput.value = new Date().toISOString().split('T')[0];
            }

            if (checkoutInput && !checkoutInput.value) {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                checkoutInput.value = tomorrow.toISOString().split('T')[0];
            }
        });
    }

    // Enhanced scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for scroll animations
    document.querySelectorAll('.stat-card, .section-title, .section-subtitle, .modern-hotel-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Add interactive hover effects to trust indicators
    const trustItems = document.querySelectorAll('.trust-item-new');
    trustItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add smooth scrolling to "View All Hotels" button
    const viewAllBtn = document.querySelector('a[href="hotels.php"]');
    if (viewAllBtn) {
        viewAllBtn.addEventListener('click', function(e) {
            // Add a subtle loading effect
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    }

    // Enhanced input focus effects
    const inputs = document.querySelectorAll('.search-input-new');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });

    // Add parallax effect to floating shapes
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const shapes = document.querySelectorAll('.floating-shape-new');

        shapes.forEach((shape, index) => {
            const speed = 0.5 + (index * 0.1);
            shape.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
