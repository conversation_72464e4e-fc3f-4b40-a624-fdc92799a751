<?php
// Modern User Home/Landing Page
include 'includes/header.php';
include 'includes/db.php';
?>

<!-- Hero Section with Background Image -->
<section class="hero-section">
    <img src="assets/images/hotel-placeholder.jpg" alt="Hotel Background" class="hero-background">
    <div class="hero-content">
        <h1 class="hero-title">“Discover the Heart of Cabadbaran City – Where Comfort Meets Culture in Every Stay.”</h1>
        <p class="hero-subtitle">Experience the best of Cabadbaran City with our handpicked hotels, each offering genuine hospitality, modern amenities, and a gateway to the city’s vibrant life. Book now and make your visit truly unforgettable!</p>
        <div class="d-flex justify-content-center gap-3">
            <a href="#featured-hotels" class="btn btn-primary btn-lg">Explore Top Hotels</a>
            <a href="#featured-rooms" class="btn btn-outline-light btn-lg">See Featured Rooms</a>
        </div>
    </div>
</section>

<!-- Cabadbaran City Best Quote Section -->
<section class="py-5 bg-primary text-white text-center">
    <div class="container">
        <h1 class="display-5 fw-bold mb-3">“Discover the Heart of Agusan del Norte – Where Comfort Meets Culture in Cabadbaran City Hotels”</h1>
        <p class="lead mb-0">Experience genuine hospitality, modern amenities, and the warmth of Cabadbaran. Your unforgettable stay starts here.</p>
    </div>
</section>

<!-- 7 Real Hotels Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Top Hotels in Cabadbaran City</h2>
            <p class="text-muted">Handpicked hotels with real comfort, prime locations, and authentic local charm</p>
        </div>
        <div class="row g-4">
            <!-- Hotel 1 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/gazebo_pools.jpg" class="card-img-top" alt="Gazebo Pools Resort">
                    <div class="card-body">
                        <h5 class="card-title">Gazebo Pools Resort</h5>
                        <p class="card-text">A family-friendly resort with spacious pools, lush gardens, and cozy rooms. Perfect for relaxation and gatherings.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> Purok 6, Barangay 6, Cabadbaran City</div>
                    </div>
                </div>
            </div>
            <!-- Hotel 2 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/casa_alburo.jpg" class="card-img-top" alt="Casa Alburo Hotel">
                    <div class="card-body">
                        <h5 class="card-title">Casa Alburo Hotel</h5>
                        <p class="card-text">Modern comfort in the city center. Enjoy stylish rooms, a café, and easy access to Cabadbaran’s main attractions.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> J.P. Rizal St., Cabadbaran City</div>
                    </div>
                </div>
            </div>
            <!-- Hotel 3 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/eg_hotel.jpg" class="card-img-top" alt="EG Hotel & Convention Center">
                    <div class="card-body">
                        <h5 class="card-title">EG Hotel & Convention Center</h5>
                        <p class="card-text">The city’s premier venue for events and business travelers. Spacious rooms, function halls, and excellent service.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> National Highway, Cabadbaran City</div>
                    </div>
                </div>
            </div>
            <!-- Hotel 4 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/j_jm_hotel.jpg" class="card-img-top" alt="J-JM Hotel">
                    <div class="card-body">
                        <h5 class="card-title">J-JM Hotel</h5>
                        <p class="card-text">Affordable comfort with a local touch. Clean rooms, friendly staff, and a convenient location for travelers.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> Purok 2, Barangay 2, Cabadbaran City</div>
                    </div>
                </div>
            </div>
            <!-- Hotel 5 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/ladolcevita_resort.jpg" class="card-img-top" alt="La Dolce Vita Resort">
                    <div class="card-body">
                        <h5 class="card-title">La Dolce Vita Resort</h5>
                        <p class="card-text">A peaceful escape with Italian-inspired architecture, gardens, and a relaxing pool area. Ideal for couples and families.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> Brgy. Calamba, Cabadbaran City</div>
                    </div>
                </div>
            </div>
            <!-- Hotel 6 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/manding_loreta.jpg" class="card-img-top" alt="Manding Loreta Hotel">
                    <div class="card-body">
                        <h5 class="card-title">Manding Loreta Hotel</h5>
                        <p class="card-text">A favorite for business and leisure. Comfortable rooms, a restaurant, and easy access to city landmarks.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> Brgy. 1, Cabadbaran City</div>
                    </div>
                </div>
            </div>
            <!-- Hotel 7 -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow">
                    <img src="assets/images/hotels/ruth_apartelle_suite.jpg" class="card-img-top" alt="Ruth Apartelle & Suite">
                    <div class="card-body">
                        <h5 class="card-title">Ruth Apartelle & Suite</h5>
                        <p class="card-text">Spacious suites for long stays. Homey atmosphere, kitchenettes, and a quiet neighborhood setting.</p>
                        <div class="text-muted"><i class="fas fa-map-marker-alt"></i> Purok 4, Barangay 4, Cabadbaran City</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Bar Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="card border-0 shadow">
            <div class="card-body p-4">
                <form action="hotels.php" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="destination" class="form-label">Destination</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="destination" name="city" placeholder="Where are you going?">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="check-in" class="form-label">Check In</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control datepicker" id="check-in" placeholder="Select date">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="check-out" class="form-label">Check Out</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control datepicker" id="check-out" placeholder="Select date">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Why Book With Us Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Why Book With Us?</h2>
            <p class="text-muted">Experience the difference with our innovative hotel booking platform</p>
        </div>
        <div class="row g-4 justify-content-center">
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-microchip"></i>
                    </div>
                    <h5>Smart Room Innovations</h5>
                    <p class="text-muted">Control your room with your voice, enjoy digital concierge, and experience the latest in hotel technology.</p>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-leaf"></i>
                    </div>
                    <h5>Sustainable Stays</h5>
                    <p class="text-muted">Book eco-friendly hotels with verified sustainability initiatives and make a positive impact with every stay.</p>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-star"></i>
                    </div>
                    <h5>Personalized Experience</h5>
                    <p class="text-muted">Get recommendations tailored to your preferences and enjoy seamless, real-time booking and support.</p>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="card border-0 h-100 text-center p-4 animate-on-scroll">
                    <div class="feature-icon bg-danger text-white rounded-circle mx-auto mb-4">
                        <i class="fa fa-shield-alt"></i>
                    </div>
                    <h5>Secure Booking</h5>
                    <p class="text-muted">Book with confidence knowing your personal and payment information is protected with advanced security.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Room Types Section -->
<section id="featured-rooms" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Featured Room Types</h2>
            <p class="text-muted">Choose from our most popular and comfortable room types</p>
        </div>

        <div class="row g-4">
            <!-- Standard Room -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow room-card">
                    <div id="standardRoomCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#standardRoomCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                            <button type="button" data-bs-target="#standardRoomCarousel" data-bs-slide-to="1"></button>
                            <button type="button" data-bs-target="#standardRoomCarousel" data-bs-slide-to="2"></button>
                        </div>
                        <div class="carousel-inner rounded-top">
                            <div class="carousel-item active">
                                <img src="assets/images/rooms/standard-1.jpg" class="d-block w-100 room-img" alt="Standard Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/standard-2.jpg" class="d-block w-100 room-img" alt="Standard Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/standard-3.jpg" class="d-block w-100 room-img" alt="Standard Room">
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#standardRoomCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#standardRoomCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="card-title mb-0">Standard Room</h5>
                            <span class="badge bg-primary">From ₱1,500/night</span>
                        </div>
                        <p class="card-text">Comfortable and affordable rooms with all the essential amenities for a pleasant stay. Perfect for budget-conscious travelers who don't want to compromise on quality.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="d-inline-block me-2"><i class="fas fa-wifi text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-tv text-muted"></i></span>
                                <span class="d-inline-block"><i class="fas fa-shower text-muted"></i></span>
                            </div>
                            <a href="room.php?type=standard" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Deluxe Room -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow room-card">
                    <div id="deluxeRoomCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#deluxeRoomCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                            <button type="button" data-bs-target="#deluxeRoomCarousel" data-bs-slide-to="1"></button>
                            <button type="button" data-bs-target="#deluxeRoomCarousel" data-bs-slide-to="2"></button>
                        </div>
                        <div class="carousel-inner rounded-top">
                            <div class="carousel-item active">
                                <img src="assets/images/rooms/deluxe-1.jpg" class="d-block w-100 room-img" alt="Deluxe Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/deluxe-2.jpg" class="d-block w-100 room-img" alt="Deluxe Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/deluxe-3.jpg" class="d-block w-100 room-img" alt="Deluxe Room">
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#deluxeRoomCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#deluxeRoomCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="card-title mb-0">Deluxe Room</h5>
                            <span class="badge bg-primary">From ₱2,500/night</span>
                        </div>
                        <p class="card-text">Modern comfort with elegant design, featuring upgraded amenities and more space. Perfect for couples or solo travelers looking for a touch of luxury.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="d-inline-block me-2"><i class="fas fa-wifi text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-tv text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-coffee text-muted"></i></span>
                                <span class="d-inline-block"><i class="fas fa-bath text-muted"></i></span>
                            </div>
                            <a href="room.php?type=deluxe" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suite Room -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow room-card">
                    <div id="suiteRoomCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#suiteRoomCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                            <button type="button" data-bs-target="#suiteRoomCarousel" data-bs-slide-to="1"></button>
                            <button type="button" data-bs-target="#suiteRoomCarousel" data-bs-slide-to="2"></button>
                        </div>
                        <div class="carousel-inner rounded-top">
                            <div class="carousel-item active">
                                <img src="assets/images/rooms/suite-1.jpg" class="d-block w-100 room-img" alt="Suite Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/suite-2.jpg" class="d-block w-100 room-img" alt="Suite Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/suite-3.jpg" class="d-block w-100 room-img" alt="Suite Room">
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#suiteRoomCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#suiteRoomCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="card-title mb-0">Suite Room</h5>
                            <span class="badge bg-primary">From ₱3,500/night</span>
                        </div>
                        <p class="card-text">Luxury and space combined, with separate living area and premium amenities. Perfect for those seeking an upscale experience or longer stays.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="d-inline-block me-2"><i class="fas fa-wifi text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-tv text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-coffee text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-bath text-muted"></i></span>
                                <span class="d-inline-block"><i class="fas fa-couch text-muted"></i></span>
                            </div>
                            <a href="room.php?type=suite" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Room -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow room-card">
                    <div id="familyRoomCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#familyRoomCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                            <button type="button" data-bs-target="#familyRoomCarousel" data-bs-slide-to="1"></button>
                            <button type="button" data-bs-target="#familyRoomCarousel" data-bs-slide-to="2"></button>
                        </div>
                        <div class="carousel-inner rounded-top">
                            <div class="carousel-item active">
                                <img src="assets/images/rooms/family-1.jpg" class="d-block w-100 room-img" alt="Family Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/family-2.jpg" class="d-block w-100 room-img" alt="Family Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/family-3.jpg" class="d-block w-100 room-img" alt="Family Room">
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#familyRoomCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#familyRoomCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="card-title mb-0">Family Room</h5>
                            <span class="badge bg-primary">From ₱4,000/night</span>
                        </div>
                        <p class="card-text">Spacious accommodations designed for families, with multiple beds and child-friendly amenities. Perfect for creating memorable family vacations.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="d-inline-block me-2"><i class="fas fa-wifi text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-tv text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-coffee text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-bath text-muted"></i></span>
                                <span class="d-inline-block"><i class="fas fa-child text-muted"></i></span>
                            </div>
                            <a href="room.php?type=family" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Executive Room -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow room-card">
                    <div id="executiveRoomCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#executiveRoomCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                            <button type="button" data-bs-target="#executiveRoomCarousel" data-bs-slide-to="1"></button>
                            <button type="button" data-bs-target="#executiveRoomCarousel" data-bs-slide-to="2"></button>
                        </div>
                        <div class="carousel-inner rounded-top">
                            <div class="carousel-item active">
                                <img src="assets/images/rooms/executive-1.jpg" class="d-block w-100 room-img" alt="Executive Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/executive-2.jpg" class="d-block w-100 room-img" alt="Executive Room">
                            </div>
                            <div class="carousel-item">
                                <img src="assets/images/rooms/executive-3.jpg" class="d-block w-100 room-img" alt="Executive Room">
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#executiveRoomCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#executiveRoomCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="card-title mb-0">Executive Room</h5>
                            <span class="badge bg-primary">From ₱3,000/night</span>
                        </div>
                        <p class="card-text">Designed for business travelers, featuring dedicated workspace, high-speed internet, and premium amenities. Perfect for combining work and relaxation.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="d-inline-block me-2"><i class="fas fa-wifi text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-tv text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-coffee text-muted"></i></span>
                                <span class="d-inline-block me-2"><i class="fas fa-bath text-muted"></i></span>
                                <span class="d-inline-block"><i class="fas fa-briefcase text-muted"></i></span>
                            </div>
                            <a href="room.php?type=executive" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 text-center mt-4">
                <a href="rooms.php" class="btn btn-primary">View All Room Types</a>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">What Our Guests Say</h2>
            <p class="text-muted">Read testimonials from our satisfied customers</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <?php
                        try {
                            // Initialize testimonials variable
                            $testimonials = false;

                            // Check if the reviews table exists
                            $table_check = $mysqli->query("SHOW TABLES LIKE 'reviews'");

                            if ($table_check && $table_check->num_rows > 0) {
                                // Check if required related tables exist
                                $required_tables = ['bookings', 'users', 'rooms', 'hotels'];
                                $missing_tables = [];

                                foreach ($required_tables as $table) {
                                    $result = $mysqli->query("SHOW TABLES LIKE '$table'");
                                    if (!$result || $result->num_rows == 0) {
                                        $missing_tables[] = $table;
                                    }
                                }

                                if (empty($missing_tables)) {
                                    // All tables exist, fetch testimonials
                                    $testimonials = $mysqli->query("SELECT r.*, u.first_name, u.last_name, h.hotel_name
                                                                  FROM reviews r
                                                                  JOIN bookings b ON r.booking_id = b.booking_id
                                                                  JOIN users u ON b.user_id = u.user_id
                                                                  JOIN rooms rm ON b.room_id = rm.room_id
                                                                  JOIN hotels h ON rm.hotel_id = h.hotel_id
                                                                  WHERE r.rating >= 4
                                                                  ORDER BY r.created_at DESC LIMIT 5");
                                }
                            }
                        } catch (Exception $e) {
                            // Handle exception silently
                            $testimonials = false;
                        }

                        $active = true;
                        $has_testimonials = false;

                        if ($testimonials && $testimonials->num_rows > 0):
                            $has_testimonials = true;
                            while($testimonial = $testimonials->fetch_assoc()):
                        ?>
                        <div class="carousel-item <?= $active ? 'active' : '' ?>">
                            <div class="card border-0 shadow-sm p-4 testimonial-card">
                                <div class="row">
                                    <div class="col-md-3 text-center mb-3 mb-md-0">
                                        <div class="testimonial-avatar mx-auto mb-3">
                                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                                        </div>
                                        <h5 class="mb-0"><?= htmlspecialchars($testimonial['first_name'] . ' ' . substr($testimonial['last_name'], 0, 1) . '.') ?></h5>
                                        <p class="text-muted small mb-2">Stayed at</p>
                                        <p class="fw-bold text-primary mb-0"><?= htmlspecialchars($testimonial['hotel_name']) ?></p>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex mb-3">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?= $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted' ?> me-1"></i>
                                            <?php endfor; ?>
                                        </div>
                                        <p class="testimonial-text fs-5 fst-italic mb-3">"<?= htmlspecialchars($testimonial['comment']) ?>"</p>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar-alt me-1"></i> <?= date('F Y', strtotime($testimonial['created_at'])) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                            $active = false;
                            endwhile;
                        endif;

                        // If no testimonials, show default ones
                        if (!$has_testimonials):
                            // Default testimonial 1
                        ?>
                        <div class="carousel-item active">
                            <div class="card border-0 shadow-sm p-4 testimonial-card">
                                <div class="row">
                                    <div class="col-md-3 text-center mb-3 mb-md-0">
                                        <div class="testimonial-avatar mx-auto mb-3">
                                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                                        </div>
                                        <h5 class="mb-0">John D.</h5>
                                        <p class="text-muted small mb-2">Stayed at</p>
                                        <p class="fw-bold text-primary mb-0">Gazebo Pools Resort</p>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex mb-3">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                        </div>
                                        <p class="testimonial-text fs-5 fst-italic mb-3">"Amazing experience! The room was spacious and clean, the staff was very attentive, and the amenities were top-notch. Will definitely book again on my next trip to Cabadbaran City."</p>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar-alt me-1"></i> May 2023
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Default testimonial 2 -->
                        <div class="carousel-item">
                            <div class="card border-0 shadow-sm p-4 testimonial-card">
                                <div class="row">
                                    <div class="col-md-3 text-center mb-3 mb-md-0">
                                        <div class="testimonial-avatar mx-auto mb-3">
                                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                                        </div>
                                        <h5 class="mb-0">Maria S.</h5>
                                        <p class="text-muted small mb-2">Stayed at</p>
                                        <p class="fw-bold text-primary mb-0">Casa Alburo Hotel</p>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex mb-3">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                        </div>
                                        <p class="testimonial-text fs-5 fst-italic mb-3">"Perfect location in the heart of Cabadbaran City! The hotel staff went above and beyond to make our family vacation special. The rooms were comfortable and the breakfast was delicious."</p>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar-alt me-1"></i> April 2023
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Default testimonial 3 -->
                        <div class="carousel-item">
                            <div class="card border-0 shadow-sm p-4 testimonial-card">
                                <div class="row">
                                    <div class="col-md-3 text-center mb-3 mb-md-0">
                                        <div class="testimonial-avatar mx-auto mb-3">
                                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                                        </div>
                                        <h5 class="mb-0">Robert T.</h5>
                                        <p class="text-muted small mb-2">Stayed at</p>
                                        <p class="fw-bold text-primary mb-0">EG Hotel & Convention Center</p>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex mb-3">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <i class="fas fa-star text-muted me-1"></i>
                                        </div>
                                        <p class="testimonial-text fs-5 fst-italic mb-3">"Great business hotel with excellent conference facilities. The internet was fast and reliable, and the meeting rooms were well-equipped. Would recommend for business travelers."</p>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar-alt me-1"></i> March 2023
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="carousel-indicators position-relative mt-3">
                        <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                        <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="1"></button>
                        <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="2"></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-primary text-white text-center">
    <div class="container">
        <h2 class="fw-bold mb-3">Ready to Experience the Future of Hotel Booking?</h2>
        <p class="mb-4">Join thousands of satisfied customers who have transformed their travel experience with SmartHotel.</p>
        <div class="d-flex justify-content-center gap-3">
            <a href="hotels.php" class="btn btn-light btn-lg">Find Hotels</a>
            <a href="register.php" class="btn btn-outline-light btn-lg">Sign Up Now</a>
        </div>
    </div>
</section>

<style>
/* Hero Section Styles */
.hero-section {
    position: relative;
    height: 600px;
    overflow: hidden;
    color: white;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.6);
}

.hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 150px 20px;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Feature Icons */
.feature-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    border-radius: 50%;
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-icon:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.15);
}

/* Carousel Controls */
.carousel-control-prev-icon, .carousel-control-next-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 1.2rem;
    background-color: rgba(0,0,0,0.5);
    border-radius: 50%;
}

.carousel-indicators button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0 5px;
}

/* Room Cards */
.room-card {
    transition: transform 0.3s, box-shadow 0.3s;
    border-radius: 0.5rem;
    overflow: hidden;
}

.room-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.room-img {
    height: 220px;
    object-fit: cover;
}

.card-body {
    padding: 1.5rem;
}

.badge {
    padding: 0.5rem 0.8rem;
    font-weight: 500;
    border-radius: 30px;
}

/* Testimonial Cards */
.testimonial-card {
    border-radius: 0.5rem;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.testimonial-avatar {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.1);
    margin-bottom: 1rem;
}

.testimonial-text {
    position: relative;
    padding-left: 1.5rem;
    line-height: 1.6;
}

.testimonial-text::before {
    content: '\201C';
    position: absolute;
    left: 0;
    top: -10px;
    font-size: 3rem;
    color: rgba(13, 110, 253, 0.2);
    font-family: Georgia, serif;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #dee2e6;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.carousel-indicators .active {
    background-color: #0d6efd;
    opacity: 1;
    transform: scale(1.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .room-img {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: 500px;
    }

    .hero-content {
        padding: 100px 20px;
    }
}
</style>

<script>
// Initialize all room carousels with enhanced features
document.addEventListener('DOMContentLoaded', function() {
    // Get all room carousels
    const roomCarousels = [
        'standardRoomCarousel',
        'deluxeRoomCarousel',
        'suiteRoomCarousel',
        'familyRoomCarousel',
        'executiveRoomCarousel'
    ];

    // Configure each carousel
    roomCarousels.forEach(carouselId => {
        const carouselElement = document.getElementById(carouselId);
        if (carouselElement) {
            // Create Bootstrap carousel instance with custom options
            const carousel = new bootstrap.Carousel(carouselElement, {
                interval: 5000,  // 5 seconds per slide
                wrap: true,      // Continuous loop
                touch: true,     // Enable touch swiping on mobile
                pause: 'hover'   // Pause on hover
            });

            // Add hover pause functionality
            carouselElement.addEventListener('mouseenter', function() {
                carousel.pause();
            });

            carouselElement.addEventListener('mouseleave', function() {
                carousel.cycle();
            });
        }
    });

    // Add animation to room cards
    const roomCards = document.querySelectorAll('.room-card');

    // Create intersection observer for animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    // Observe each room card
    roomCards.forEach(card => {
        observer.observe(card);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
