<?php
// Working Modern Landing Page
include 'includes/header.php';
include 'includes/db.php';

// Get dynamic data for the homepage with error handling
try {
    $hotel_count = $mysqli->query("SELECT COUNT(*) as count FROM hotels")->fetch_assoc()['count'];
    $room_count = $mysqli->query("SELECT COUNT(*) as count FROM rooms")->fetch_assoc()['count'];
    $booking_count = $mysqli->query("SELECT COUNT(*) as count FROM bookings WHERE payment_status = 'paid'")->fetch_assoc()['count'];
} catch (Exception $e) {
    $hotel_count = 7;
    $room_count = 420;
    $booking_count = 150;
}
?>

<!-- Modern Typography -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

<style>
/* Ultra-Modern UI Design System */
:root {
    /* Modern Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    --accent-gradient: linear-gradient(135deg, #00d4aa 0%, #00a085 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    /* Neumorphism */
    --neu-light: #ffffff;
    --neu-dark: #d1d9e6;
    --neu-shadow-light: 20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff;
    --neu-shadow-inset: inset 20px 20px 60px #d1d9e6, inset -20px -20px 60px #ffffff;

    /* Modern Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Poppins', 'Inter', sans-serif;

    /* Spacing & Sizing */
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: #2c3e50;
    overflow-x: hidden;
    scroll-behavior: smooth;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Modern Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Modern Hero Background with Floating Elements */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    animation: slideshow 25s infinite, parallaxFloat 20s ease-in-out infinite;
}

@keyframes slideshow {
    0%, 20% { background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80'); }
    25%, 45% { background-image: url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=1920&q=80'); }
    50%, 70% { background-image: url('https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?auto=format&fit=crop&w=1920&q=80'); }
    75%, 95% { background-image: url('https://images.unsplash.com/photo-1564501049412-61c2a3083791?auto=format&fit=crop&w=1920&q=80'); }
    100% { background-image: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=1920&q=80'); }
}

@keyframes parallaxFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-20px) scale(1.02); }
}

/* Floating Geometric Shapes */
.hero-section::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 100px;
    height: 100px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 15%;
    width: 80px;
    height: 80px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    animation: float 8s ease-in-out infinite reverse;
    z-index: 1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

/* Modern Hero Content */
.hero-content {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    padding: 3rem 2rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
}

/* Modern Typography */
.hero-title {
    font-family: var(--font-display);
    font-size: clamp(2.8rem, 6vw, 5rem);
    font-weight: 800;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    letter-spacing: -0.02em;
    line-height: 1.1;
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}



.hero-subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.6rem);
    margin-bottom: 3rem;
    opacity: 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

/* Text Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}



/* Ultra-Modern Search Form */
.search-form {
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    margin-bottom: 3rem;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0;
    transform: scale(0.95);
    transition: var(--transition);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
}

.search-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.8s ease;
}

.search-form:hover::before {
    left: 100%;
}

.search-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

/* Modern Input Fields */
.search-input {
    padding: 1.2rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    font-size: 1rem;
    font-weight: 500;
    color: #2c3e50;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-gradient);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 170, 0.2);
}

.search-input::placeholder {
    color: #6c757d;
    font-weight: 400;
}

/* Ultra-Modern Search Button */
.search-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 1.2rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.search-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 212, 170, 0.4);
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn:active {
    transform: translateY(-1px);
}

/* Modern Feature Badges */
.features {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
    position: relative;
    z-index: 2;
}

.feature-item {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    padding: 1rem 2rem;
    border-radius: 50px;
    backdrop-filter: blur(15px);
    transition: var(--transition);
    font-weight: 500;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.95);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-item:hover::before {
    left: 100%;
}

/* Ultra-Modern Stats Section */
.stats-section {
    padding: 8rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: rotate 30s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

/* Modern Grid Layout */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

/* Neumorphism Stat Cards */
.stat-card {
    background: var(--neu-light);
    padding: 3rem 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--neu-shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--accent-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stat-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Modern Stat Typography */
.stat-number {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 4vw, 3.5rem);
    font-weight: 800;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    line-height: 1;
    letter-spacing: -0.02em;
}

.stat-label {
    color: #5a6c7d;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Modern Section Headers */
.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    text-align: center;
    margin-bottom: 1.5rem;
    background: var(--dark-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.1;
    animation: slideInLeft 1s ease-out;
}

.section-subtitle {
    text-align: center;
    color: #5a6c7d;
    font-size: clamp(1.1rem, 2vw, 1.4rem);
    max-width: 700px;
    margin: 0 auto;
    font-weight: 400;
    line-height: 1.6;
    animation: slideInRight 1s ease-out 0.2s both;
}

.stats-grid {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.stat-card {
    animation: zoomIn 0.6s ease-out both;
}

.stat-card:nth-child(1) { animation-delay: 0.6s; }
.stat-card:nth-child(2) { animation-delay: 0.8s; }
.stat-card:nth-child(3) { animation-delay: 1.0s; }
.stat-card:nth-child(4) { animation-delay: 1.2s; }

/* Ultra-Modern Hotel Cards */
.modern-hotel-card {
    background: var(--neu-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--neu-shadow-light);
    transition: var(--transition);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.modern-hotel-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.hotel-image-container {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: var(--primary-gradient);
}

.hotel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.modern-hotel-card:hover .hotel-image {
    transform: scale(1.1);
}

.hotel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.2) 100%);
    opacity: 0;
    transition: var(--transition);
}

.modern-hotel-card:hover .hotel-overlay {
    opacity: 1;
}

.featured-badge {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    background: var(--accent-gradient);
    color: white;
    padding: 0.7rem 1.2rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
    backdrop-filter: blur(10px);
}

.hotel-rating-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.7rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    backdrop-filter: blur(10px);
}

.hotel-rating-badge i {
    color: #ffd700;
}

/* Modern Hotel Content */
.hotel-content {
    padding: 2.5rem;
}

.hotel-title {
    font-family: var(--font-display);
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
    line-height: 1.3;
}

.hotel-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #5a6c7d;
    font-weight: 500;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.hotel-location i {
    color: var(--accent-gradient);
    font-size: 0.9rem;
}

.hotel-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.hotel-amenities {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.amenity-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #4e73df;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.amenity-tag i {
    font-size: 0.75rem;
}

.hotel-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.price-section {
    display: flex;
    flex-direction: column;
}

.price {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 800;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.price-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.modern-btn {
    background: var(--secondary-gradient);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.3);
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover i {
    transform: translateX(3px);
}

@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
    }

    .features {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background"></div>
    <div class="hero-content">
        <h1 class="hero-title" id="typewriter-title"></h1>
        <p class="hero-subtitle">Experience the perfect blend of luxury, comfort, and authentic local culture with our handpicked selection of premium accommodations.</p>

        <form class="search-form" action="hotels.php" method="get">
            <div class="search-row">
                <input type="text" name="city" placeholder="Where are you going?" class="search-input" value="Cabadbaran City">
                <input type="date" name="check_in" class="search-input" min="<?= date('Y-m-d') ?>">
                <input type="date" name="check_out" class="search-input" min="<?= date('Y-m-d', strtotime('+1 day')) ?>">
                <button type="submit" class="search-btn">Search Hotels</button>
            </div>
        </form>

        <div class="features">
            <div class="feature-item">✓ Best Price Guarantee</div>
            <div class="feature-item">✓ Free Cancellation</div>
            <div class="feature-item">✓ 24/7 Support</div>
            <div class="feature-item">✓ Secure Booking</div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <h2 class="section-title">Why Choose Our Platform</h2>
        <p class="section-subtitle">Trusted by thousands of travelers for exceptional hotel booking experiences</p>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $hotel_count ?></div>
                <div class="stat-label">Premium Hotels</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $room_count ?></div>
                <div class="stat-label">Available Rooms</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $booking_count + 150 ?></div>
                <div class="stat-label">Happy Guests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4.8</div>
                <div class="stat-label">Average Rating</div>
            </div>
        </div>
    </div>
</section>

<?php
// Get featured hotels
try {
    $hotels_query = "SELECT h.*,
                           (SELECT MIN(price) FROM rooms WHERE hotel_id = h.hotel_id) as min_price
                    FROM hotels h
                    ORDER BY h.hotel_id
                    LIMIT 6";
    $hotels_result = $mysqli->query($hotels_query);
} catch (Exception $e) {
    $hotels_result = false;
}
?>

<!-- Featured Hotels Section -->
<section style="padding: 5rem 0;">
    <div class="container">
        <h2 class="section-title">Featured Hotels</h2>
        <p class="section-subtitle">Handpicked accommodations offering the perfect blend of comfort and luxury</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 3rem;">
            <?php if ($hotels_result && $hotels_result->num_rows > 0): ?>
                <?php
                $card_index = 0;
                while($hotel = $hotels_result->fetch_assoc()):
                    // Hotel image mapping by hotel_id for accuracy
                    $hotel_images = [
                        1 => 'e_&_g_hotel_and_restaurant.jpg',
                        2 => 'casa_alburo_hotel_and_restaurant.jpg',
                        3 => 'gazebo_pools_and_restaurant.jpg',
                        4 => 'j&jm.jpg',
                        5 => 'manding_loreta_resort.jpg',
                        6 => 'ladolcevita_inland_resort.jpg',
                        7 => 'ruth_apartelle_suite.jpg'
                    ];
                    $hotel_image = $hotel_images[$hotel['hotel_id']] ?? 'hotel-placeholder.jpg';
                    $animation_delay = 0.2 * $card_index;
                    $card_index++;
                ?>
                <div class="modern-hotel-card" style="animation: fadeInUp 0.6s ease-out <?= $animation_delay ?>s both;">
                    <div class="hotel-image-container">
                        <img src="assets/images/hotels/<?= $hotel_image ?>"
                             alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                             class="hotel-image"
                             onerror="this.style.display='none'">
                        <div class="hotel-overlay"></div>
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                            Featured
                        </div>
                        <div class="hotel-rating-badge">
                            <i class="fas fa-star"></i>
                            4.8
                        </div>
                    </div>
                    <div class="hotel-content">
                        <h3 class="hotel-title"><?= htmlspecialchars($hotel['hotel_name']) ?></h3>
                        <div class="hotel-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <?= htmlspecialchars($hotel['city']) ?>
                        </div>
                        <p class="hotel-description"><?= htmlspecialchars(substr($hotel['description'] ?? 'Experience comfort and luxury at this premier hotel location.', 0, 120)) ?>...</p>

                        <div class="hotel-amenities">
                            <span class="amenity-tag"><i class="fas fa-wifi"></i> Free WiFi</span>
                            <span class="amenity-tag"><i class="fas fa-swimming-pool"></i> Pool</span>
                            <span class="amenity-tag"><i class="fas fa-utensils"></i> Restaurant</span>
                        </div>

                        <div class="hotel-footer">
                            <div class="price-section">
                                <div class="price">₱<?= number_format($hotel['min_price'] ?? 1500, 0) ?></div>
                                <div class="price-label">per night</div>
                            </div>
                            <a href="hotel_details.php?id=<?= $hotel['hotel_id'] ?>" class="modern-btn">
                                <span>View Details</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #6c757d;">
                    <h3>Hotels Coming Soon</h3>
                    <p>We're working on adding amazing hotels to our platform.</p>
                </div>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 3rem;">
            <a href="hotels.php" style="background: var(--secondary-gradient); color: white; padding: 1rem 3rem; border-radius: 10px; text-decoration: none; font-weight: 600; font-size: 1.1rem;">View All Hotels</a>
        </div>
    </div>
</section>

<script>
// Typewriter Effect for Hero Title
document.addEventListener('DOMContentLoaded', function() {
    const titleElement = document.getElementById('typewriter-title');
    const subtitleElement = document.querySelector('.hero-subtitle');
    const searchForm = document.querySelector('.search-form');
    const features = document.querySelector('.features');
    const text = 'Discover Extraordinary Hotels in Cabadbaran City';
    let index = 0;

    // Add cursor
    titleElement.style.borderRight = '3px solid white';
    titleElement.style.paddingRight = '5px';

    function typeWriter() {
        if (index < text.length) {
            titleElement.textContent += text.charAt(index);
            index++;
            setTimeout(typeWriter, 80); // Adjust speed here (lower = faster)
        } else {
            // Animation sequence after typing completes
            setTimeout(() => {
                // Animate subtitle
                subtitleElement.style.opacity = '0.95';
                subtitleElement.style.transform = 'translateY(0)';

                // Animate search form
                setTimeout(() => {
                    searchForm.style.opacity = '1';
                    searchForm.style.transform = 'scale(1)';
                }, 500);

                // Animate features
                setTimeout(() => {
                    features.style.opacity = '1';
                    features.style.transform = 'translateY(0)';
                }, 1000);

                // Start blinking cursor
                setInterval(() => {
                    titleElement.style.borderRight = titleElement.style.borderRight === '3px solid white'
                        ? '3px solid transparent'
                        : '3px solid white';
                }, 500);
            }, 500);
        }
    }

    // Start typing after a delay
    setTimeout(typeWriter, 1000);
});

// Scroll animations for other elements
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for scroll animations
    document.querySelectorAll('.stat-card, .section-title, .section-subtitle').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
