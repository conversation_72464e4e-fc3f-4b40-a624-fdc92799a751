<?php
// Modern User Home/Landing Page
include 'includes/header.php';
include 'includes/db.php';
?>

<style>
/* Global Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #0f172a;
    --accent-color: #f59e0b;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --text-color: #334155;
    --text-light: #64748b;
    --border-radius: 12px;
    --box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

/* Animated Background */
.page-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.background-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.shape {
    position: absolute;
    opacity: 0.05;
    border-radius: 50%;
    background: var(--primary-color);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    min-height: 700px;
    display: flex;
    align-items: center;
    overflow: hidden;
    color: white;
}

.hero-background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.5);
    transform: scale(1.05);
    transition: transform 10s ease;
}

.hero-background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(15, 23, 42, 0.7), rgba(15, 23, 42, 0.9));
    z-index: 1;
}

.hero-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0djZoNnYtNmgtNnptNi02aDZ2LTZoLTZ2NnptLTYgMGg2di02aC02djZ6bS02IDZoNnYtNmgtNnY2em0xMi0xMmg2di02aC02djZ6bS02IDBoNnYtNmgtNnY2em0tNi02aDZ2LTZoLTZ2NnptLTYgNmg2di02aC02djZ6bS02IDZoNnYtNmgtNnY2eiIvPjwvZz48L2c+PC9zdmc+');
    opacity: 0.1;
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 10;
    padding: 2rem 0;
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.text-gradient {
    background: linear-gradient(90deg, #f59e0b, #ef4444);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.hero-search-container {
    max-width: 900px;
    margin: 0 auto 2rem;
}

.hero-search-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input-group {
    position: relative;
    padding: 0.75rem 1rem;
}

.search-input-group i {
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-input-group .form-control {
    padding-left: 2.5rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: var(--border-radius);
}

.search-btn {
    height: 3rem;
    width: 100%;
    border-radius: var(--border-radius);
    background: var(--primary-color);
    border: none;
    font-weight: 600;
    transition: var(--transition);
}

.search-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

.hero-features {
    margin-top: 2rem;
}

.hero-feature-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin: 0 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-feature-item i {
    color: var(--accent-color);
    margin-right: 0.5rem;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.scroll-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    animation: bounce 2s infinite;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.hero-scroll-indicator a {
    color: white;
    text-decoration: none;
    font-size: 0.875rem;
    opacity: 0.8;
    transition: var(--transition);
}

.hero-scroll-indicator a:hover {
    opacity: 1;
}

/* Experience Section */
.experience-section {
    background-color: white;
    position: relative;
    overflow: hidden;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto 2rem;
}

.experience-feature {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: var(--transition);
}

.experience-feature:hover {
    transform: translateY(-5px);
}

.feature-icon-box {
    width: 60px;
    height: 60px;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-weight: 500;
}

/* Hotel Cards */
.hotel-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.hotel-card:hover {
    transform: translateY(-10px);
}

.hotel-card-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.hotel-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.hotel-card:hover .hotel-card-image img {
    transform: scale(1.05);
}

.hotel-card-tag {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 1;
}

.hotel-card-rating {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--accent-color);
    padding: 0.25rem 0.5rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.hotel-card-rating i {
    margin-right: 0.25rem;
}

.hotel-card-body {
    padding: 1.5rem;
}

.hotel-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.hotel-card-location {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.hotel-card-location i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.hotel-card-description {
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.hotel-card-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.hotel-card-features span {
    font-size: 0.75rem;
    color: var(--text-light);
    background: rgba(226, 232, 240, 0.5);
    padding: 0.25rem 0.5rem;
    border-radius: 30px;
}

.hotel-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    padding-top: 1rem;
}

.hotel-card-price {
    display: flex;
    flex-direction: column;
}

.hotel-card-price .price {
    font-weight: 700;
    color: var(--primary-color);
}

.hotel-card-price .per-night {
    font-size: 0.75rem;
    color: var(--text-light);
}

/* Room Cards */
.room-card {
    transition: transform 0.3s, box-shadow 0.3s;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.room-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.room-img {
    height: 220px;
    object-fit: cover;
}

.carousel-control-prev-icon, .carousel-control-next-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 1.2rem;
    background-color: rgba(0,0,0,0.5);
    border-radius: 50%;
}

.carousel-indicators button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0 5px;
}

/* Feature Icons */
.feature-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    border-radius: 50%;
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-icon:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.15);
}

/* Testimonial Cards */
.testimonial-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.testimonial-avatar {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.1);
    margin-bottom: 1rem;
}

.testimonial-text {
    position: relative;
    padding-left: 1.5rem;
    line-height: 1.6;
}

.testimonial-text::before {
    content: '\201C';
    position: absolute;
    left: 0;
    top: -10px;
    font-size: 3rem;
    color: rgba(13, 110, 253, 0.2);
    font-family: Georgia, serif;
}

/* Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .room-img {
        height: 180px;
    }

    .hero-feature-item {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: auto;
        min-height: 600px;
        padding: 6rem 0;
    }

    .hero-content {
        padding: 2rem 1rem;
    }

    .hero-search-form {
        padding: 0.25rem;
    }

    .search-input-group {
        padding: 0.5rem;
    }

    .search-input-group .form-control {
        height: 2.5rem;
    }

    .search-btn {
        height: 2.5rem;
    }
}
</style>

<!-- Add link to Animate.css for animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Add link to Inter font -->
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">

<!-- Modern Animated Hero Section -->
<section class="hero-section" id="hero">
    <div class="hero-background-container">
        <div class="hero-background-overlay"></div>
        <div class="hero-background-pattern"></div>
        <!-- Use a beautiful city/landscape hero image from Unsplash or similar -->
        <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1500&q=80" alt="Cabadbaran City Hero" class="hero-background">
    </div>

    <div class="hero-content">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <div class="hero-badge animate__animated animate__fadeInDown">
                        <span>Premium Hotel Booking Experience</span>
                    </div>
                    <h1 class="hero-title animate__animated animate__fadeInUp">
                        Discover Extraordinary <span class="text-gradient">Stays</span> in Cabadbaran City
                    </h1>
                    <p class="hero-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                        Experience the perfect blend of luxury, comfort, and authentic local culture with our handpicked selection of premium accommodations.
                    </p>

                    <div class="hero-search-container animate__animated animate__fadeInUp animate__delay-2s">
                        <form class="hero-search-form" action="hotels.php" method="get">
                            <div class="row g-0">
                                <div class="col-md-4">
                                    <div class="search-input-group">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <input type="text" name="city" placeholder="Where are you going?" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="search-input-group">
                                        <i class="fas fa-calendar-alt"></i>
                                        <input type="text" name="check_in" placeholder="Check-in Date" class="form-control datepicker">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="search-input-group">
                                        <i class="fas fa-calendar-alt"></i>
                                        <input type="text" name="check_out" placeholder="Check-out Date" class="form-control datepicker">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary search-btn">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="hero-features animate__animated animate__fadeInUp animate__delay-3s">
                        <div class="row justify-content-center">
                            <div class="col-auto">
                                <div class="hero-feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Best Price Guarantee</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="hero-feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Free Cancellation</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="hero-feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>24/7 Customer Support</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="hero-scroll-indicator animate__animated animate__fadeInUp animate__delay-4s">
        <a href="#featured-hotels">
            <div class="scroll-icon">
                <i class="fas fa-chevron-down"></i>
            </div>
            <span>Scroll to explore</span>
        </a>
    </div>
</section>

<!-- Experience Section with Stats -->
<section class="experience-section py-5" id="featured-hotels">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="experience-content">
                    <h6 class="text-primary fw-bold text-uppercase animate-on-scroll">Why Choose Us</h6>
                    <h2 class="section-title mb-4 animate-on-scroll">Experience the Best of Cabadbaran City Hospitality</h2>
                    <p class="lead mb-4 animate-on-scroll">Our curated selection of hotels combines modern luxury with authentic local experiences, ensuring your stay is nothing short of exceptional.</p>

                    <div class="row g-4 mt-2">
                        <div class="col-md-6 animate-on-scroll">
                            <div class="experience-feature">
                                <div class="feature-icon-box">
                                    <i class="fas fa-hotel"></i>
                                </div>
                                <h5>Handpicked Hotels</h5>
                                <p>Carefully selected accommodations that meet our high standards for quality and service.</p>
                            </div>
                        </div>
                        <div class="col-md-6 animate-on-scroll">
                            <div class="experience-feature">
                                <div class="feature-icon-box">
                                    <i class="fas fa-concierge-bell"></i>
                                </div>
                                <h5>Premium Service</h5>
                                <p>Exceptional customer service and attention to detail for a seamless booking experience.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="row g-4">
                    <div class="col-6 animate-on-scroll">
                        <div class="stat-card">
                            <div class="stat-number"><span class="counter">7</span>+</div>
                            <div class="stat-label">Premium Hotels</div>
                        </div>
                    </div>
                    <div class="col-6 animate-on-scroll">
                        <div class="stat-card">
                            <div class="stat-number"><span class="counter">500</span>+</div>
                            <div class="stat-label">Happy Guests</div>
                        </div>
                    </div>
                    <div class="col-6 animate-on-scroll">
                        <div class="stat-card">
                            <div class="stat-number"><span class="counter">50</span>+</div>
                            <div class="stat-label">Room Options</div>
                        </div>
                    </div>
                    <div class="col-6 animate-on-scroll">
                        <div class="stat-card">
                            <div class="stat-number"><span class="counter">100</span>%</div>
                            <div class="stat-label">Satisfaction</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Hotels Section -->
<section class="featured-hotels-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h6 class="text-primary fw-bold text-uppercase animate-on-scroll">Our Selection</h6>
            <h2 class="section-title animate-on-scroll">Top Hotels in Cabadbaran City</h2>
            <p class="section-subtitle animate-on-scroll">Handpicked hotels with real comfort, prime locations, and authentic local charm</p>
        </div>

        <div class="hotels-slider">
            <div class="row g-4">
                <!-- Hotel 1: Gazebo Pools Resort -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Popular</div>
                            <img src="assets/images/hotels/gazebo_pools.jpg" alt="Gazebo Pools Resort">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.8</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">Gazebo Pools Resort</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Purok 6, Barangay 6, Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">A family-friendly resort with spacious pools, lush gardens, and cozy rooms. Perfect for relaxation and gatherings.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-swimming-pool"></i> Pool</span>
                                <span><i class="fas fa-parking"></i> Parking</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱2,500</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=1" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hotel 2: Casa Alburo Hotel -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Best Value</div>
                            <img src="assets/images/hotels/casa_alburo.jpg" alt="Casa Alburo Hotel">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.6</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">Casa Alburo Hotel</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>J.P. Rizal St., Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">Modern comfort in the city center. Enjoy stylish rooms, a café, and easy access to Cabadbaran's main attractions.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-utensils"></i> Restaurant</span>
                                <span><i class="fas fa-concierge-bell"></i> Room Service</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱1,800</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=2" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hotel 3: EG Hotel & Convention Center -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Business</div>
                            <img src="assets/images/hotels/eg_hotel.jpg" alt="EG Hotel & Convention Center">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.7</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">EG Hotel & Convention Center</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>National Highway, Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">The city's premier venue for events and business travelers. Spacious rooms, function halls, and excellent service.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-briefcase"></i> Business Center</span>
                                <span><i class="fas fa-glass-cheers"></i> Event Space</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱2,200</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=3" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hotel 4: J-JM Hotel -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Budget</div>
                            <img src="assets/images/hotels/j&jm.jpg" alt="J-JM Hotel">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.3</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">J-JM Hotel</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Purok 2, Barangay 2, Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">Affordable comfort with a local touch. Clean rooms, friendly staff, and a convenient location for travelers.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-coffee"></i> Café</span>
                                <span><i class="fas fa-parking"></i> Parking</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱1,200</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=4" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hotel 5: La Dolce Vita Resort -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Resort</div>
                            <img src="assets/images/hotels/ladolcevita_inland_resort.jpg" alt="La Dolce Vita Resort">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.5</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">La Dolce Vita Resort</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Brgy. Calamba, Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">A peaceful escape with Italian-inspired architecture, gardens, and a relaxing pool area. Ideal for couples and families.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-swimming-pool"></i> Pool</span>
                                <span><i class="fas fa-leaf"></i> Garden</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱2,000</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=5" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hotel 6: Manding Loreta Hotel -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Classic</div>
                            <img src="assets/images/hotels/manding_loreta.jpg" alt="Manding Loreta Hotel">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.4</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">Manding Loreta Hotel</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Brgy. 1, Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">A favorite for business and leisure. Comfortable rooms, a restaurant, and easy access to city landmarks.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-utensils"></i> Restaurant</span>
                                <span><i class="fas fa-parking"></i> Parking</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱1,700</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=6" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hotel 7: Ruth Apartelle & Suite -->
                <div class="col-md-6 col-lg-4">
                    <div class="hotel-card animate-on-scroll">
                        <div class="hotel-card-image">
                            <div class="hotel-card-tag">Suite</div>
                            <img src="assets/images/hotels/ruth_apartelle_suite.jpg" alt="Ruth Apartelle & Suite">
                            <div class="hotel-card-rating">
                                <i class="fas fa-star"></i>
                                <span>4.2</span>
                            </div>
                        </div>
                        <div class="hotel-card-body">
                            <h5 class="hotel-card-title">Ruth Apartelle & Suite</h5>
                            <div class="hotel-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Purok 4, Barangay 4, Cabadbaran City</span>
                            </div>
                            <p class="hotel-card-description">Spacious suites for long stays. Homey atmosphere, kitchenettes, and a quiet neighborhood setting.</p>
                            <div class="hotel-card-features">
                                <span><i class="fas fa-wifi"></i> Free WiFi</span>
                                <span><i class="fas fa-home"></i> Kitchenette</span>
                                <span><i class="fas fa-leaf"></i> Quiet Area</span>
                            </div>
                            <div class="hotel-card-footer">
                                <div class="hotel-card-price">
                                    <span class="price">₱1,600</span>
                                    <span class="per-night">per night</span>
                                </div>
                                <a href="hotel.php?id=7" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-5">
                <a href="hotels.php" class="btn btn-outline-primary btn-lg">View All Hotels <i class="fas fa-arrow-right ms-2"></i></a>
            </div>
        </div>
    </div>
</section>

<!-- Featured Room Types Section -->
<?php
$roomTypes = [
    'deluxe' => 'Deluxe Room',
    'family' => 'Family Room',
    'suite' => 'Suite Room',
    'standard' => 'Standard Room',
    'executive' => 'Executive Room',
];
$roomDescriptions = [
    'deluxe' => 'Modern comfort with elegant design, perfect for couples or solo travelers.',
    'family' => 'Spacious and cozy, ideal for families seeking comfort and convenience.',
    'suite' => 'Luxury and space combined, with premium amenities for a special stay.',
    'standard' => 'Affordable comfort with all the essentials for a pleasant stay.',
    'executive' => 'Designed for business travelers, featuring workspaces and extra comfort.',
];
function getRoomImages($type) {
    $dir = __DIR__ . "/assets/images/rooms/";
    $images = glob($dir . $type . "*.{jpg,jpeg,png,gif,webp,avif}", GLOB_BRACE);
    $relative = [];
    foreach ($images as $img) {
        $relative[] = 'assets/images/rooms/' . basename($img);
    }
    return $relative;
}
?>
<section id="featured-rooms" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Featured Room Types</h2>
            <p class="text-muted">Choose from our most popular and comfortable room types</p>
        </div>
        <div class="row g-4 justify-content-center">
            <?php foreach ($roomTypes as $type => $label): 
                $images = getRoomImages($type);
                if (count($images) === 0) continue;
                $carouselId = $type . 'Carousel';
            ?>
            <div class="col-md-6 col-lg-4 col-xl-2">
                <div class="card h-100 border-0 shadow-sm room-card">
                    <div id="<?= $carouselId ?>" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            <?php foreach ($images as $i => $img): ?>
                            <div class="carousel-item<?= $i === 0 ? ' active' : '' ?>">
                                <img src="<?= htmlspecialchars($img) ?>" class="d-block w-100 room-img" alt="<?= htmlspecialchars($label) ?>">
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($images) > 1): ?>
                        <button class="carousel-control-prev" type="button" data-bs-target="#<?= $carouselId ?>" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#<?= $carouselId ?>" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                        <?php endif; ?>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title"><?= htmlspecialchars($label) ?></h5>
                        <p class="card-text"><?= htmlspecialchars($roomDescriptions[$type]) ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="text-center mb-5">
            <h6 class="text-primary fw-bold text-uppercase animate-on-scroll">Guest Experiences</h6>
            <h2 class="section-title animate-on-scroll">What Our Guests Say</h2>
            <p class="section-subtitle animate-on-scroll">Read testimonials from our satisfied customers</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <?php
                        try {
                            $testimonials = $mysqli->query("SELECT r.*, u.first_name, u.last_name, h.hotel_name
                                                          FROM reviews r
                                                          JOIN bookings b ON r.booking_id = b.booking_id
                                                          JOIN users u ON b.user_id = u.user_id
                                                          JOIN rooms rm ON b.room_id = rm.room_id
                                                          JOIN hotels h ON rm.hotel_id = h.hotel_id
                                                          WHERE r.rating >= 4
                                                          ORDER BY r.created_at DESC LIMIT 5");

                            $active = true;
                            if ($testimonials && $testimonials->num_rows > 0) {
                                while($testimonial = $testimonials->fetch_assoc()):
                                ?>
                                <div class="carousel-item <?= $active ? 'active' : '' ?>">
                                    <div class="card border-0 shadow p-4 testimonial-card">
                                        <div class="d-flex justify-content-between mb-3">
                                            <div>
                                                <h5 class="mb-0"><?= htmlspecialchars($testimonial['first_name'] . ' ' . substr($testimonial['last_name'], 0, 1) . '.') ?></h5>
                                                <p class="text-muted mb-0">Stayed at <?= htmlspecialchars($testimonial['hotel_name']) ?></p>
                                            </div>
                                            <div class="text-warning">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?= $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted' ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <p class="mb-0 testimonial-text"><?= htmlspecialchars($testimonial['comment']) ?></p>
                                    </div>
                                </div>
                                <?php
                                $active = false;
                                endwhile;
                            } else {
                                // If no testimonials, show default ones
                                ?>
                                <div class="carousel-item active">
                                    <div class="card border-0 shadow p-4 testimonial-card">
                                        <div class="d-flex justify-content-between mb-3">
                                            <div>
                                                <h5 class="mb-0">John D.</h5>
                                                <p class="text-muted mb-0">Stayed at Gazebo Pools Resort</p>
                                            </div>
                                            <div class="text-warning">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                            </div>
                                        </div>
                                        <p class="mb-0 testimonial-text">Amazing experience! The smart room features were incredible and the staff was very attentive. Will definitely book again on my next trip.</p>
                                    </div>
                                </div>
                                <div class="carousel-item">
                                    <div class="card border-0 shadow p-4 testimonial-card">
                                        <div class="d-flex justify-content-between mb-3">
                                            <div>
                                                <h5 class="mb-0">Maria S.</h5>
                                                <p class="text-muted mb-0">Stayed at Casa Alburo Hotel</p>
                                            </div>
                                            <div class="text-warning">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                            </div>
                                        </div>
                                        <p class="mb-0 testimonial-text">The location was perfect and the room was spotless. I especially loved the modern design and the comfortable bed. The staff went above and beyond to make our stay special.</p>
                                    </div>
                                </div>
                                <div class="carousel-item">
                                    <div class="card border-0 shadow p-4 testimonial-card">
                                        <div class="d-flex justify-content-between mb-3">
                                            <div>
                                                <h5 class="mb-0">Robert L.</h5>
                                                <p class="text-muted mb-0">Stayed at EG Hotel & Convention Center</p>
                                            </div>
                                            <div class="text-warning">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star-half-alt"></i>
                                            </div>
                                        </div>
                                        <p class="mb-0 testimonial-text">As a business traveler, I appreciate the excellent facilities and reliable internet. The meeting rooms were well-equipped and the staff was professional. Highly recommended for corporate events.</p>
                                    </div>
                                </div>
                                <?php
                            }
                        } catch (Exception $e) {
                            // In case of database error, show default testimonial
                            ?>
                            <div class="carousel-item active">
                                <div class="card border-0 shadow p-4 testimonial-card">
                                    <div class="d-flex justify-content-between mb-3">
                                        <div>
                                            <h5 class="mb-0">John D.</h5>
                                            <p class="text-muted mb-0">Stayed at Gazebo Pools Resort</p>
                                        </div>
                                        <div class="text-warning">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                    <p class="mb-0 testimonial-text">Amazing experience! The smart room features were incredible and the staff was very attentive. Will definitely book again on my next trip.</p>
                                </div>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon bg-primary rounded-circle" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon bg-primary rounded-circle" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-primary text-white text-center">
    <div class="container">
        <h2 class="fw-bold mb-3 animate-on-scroll">Ready to Experience the Future of Hotel Booking?</h2>
        <p class="mb-4 animate-on-scroll">Join thousands of satisfied customers who have transformed their travel experience with our premium hotel booking platform.</p>
        <div class="d-flex justify-content-center gap-3 animate-on-scroll">
            <a href="hotels.php" class="btn btn-light btn-lg">Find Hotels</a>
            <a href="register.php" class="btn btn-outline-light btn-lg">Sign Up Now</a>
        </div>
    </div>
</section>

<!-- Add JavaScript for animations and counters -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize scroll animations
    const animateElements = document.querySelectorAll('.animate-on-scroll');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });

    animateElements.forEach(element => {
        observer.observe(element);
    });

    // Initialize counters
    const counterElements = document.querySelectorAll('.counter');

    counterElements.forEach(counter => {
        const target = parseInt(counter.textContent);
        let count = 0;
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps

        const updateCount = () => {
            count += increment;
            if (count < target) {
                counter.textContent = Math.ceil(count);
                requestAnimationFrame(updateCount);
            } else {
                counter.textContent = target;
            }
        };

        // Start counter animation when element is in view
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCount();
                    counterObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.5
        });

        counterObserver.observe(counter.parentElement);
    });

    // Add background animation
    const createBackgroundShapes = () => {
        const pageBackground = document.createElement('div');
        pageBackground.classList.add('page-background');

        const backgroundShapes = document.createElement('div');
        backgroundShapes.classList.add('background-shapes');

        // Create random shapes
        for (let i = 0; i < 10; i++) {
            const shape = document.createElement('div');
            shape.classList.add('shape');

            const size = Math.random() * 300 + 100;
            const posX = Math.random() * 100;
            const posY = Math.random() * 100;

            shape.style.width = `${size}px`;
            shape.style.height = `${size}px`;
            shape.style.left = `${posX}%`;
            shape.style.top = `${posY}%`;

            backgroundShapes.appendChild(shape);
        }

        pageBackground.appendChild(backgroundShapes);
        document.body.prepend(pageBackground);
    };

    createBackgroundShapes();
});
</script>

<?php include 'includes/footer.php'; ?>