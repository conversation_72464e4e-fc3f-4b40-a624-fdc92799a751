<?php
if (session_status() === PHP_SESSION_NONE) session_start();
$is_logged_in = isset($_SESSION['user_id']) && $_SESSION['user_id'] > 0;
$user_img = $is_logged_in ? 'assets/images/profile-' . intval($_SESSION['user_id']) . '.jpg' : 'assets/images/user-placeholder.jpg';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartHotel - Experience the Future of Booking</title>
    <!-- Favicon -->
    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- Flatpickr for Date Picker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Ultra-Modern Glass Header -->
    <nav class="navbar navbar-expand-lg sticky-top modern-navbar">
        <div class="container">
            <a class="navbar-brand modern-brand" href="index.php">
                <div class="brand-icon">
                    <i class="fa fa-hotel"></i>
                </div>
                <span class="brand-text">SmartHotel</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="hotels.php">
                            <i class="fa fa-building"></i> Hotels
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="room.php">
                            <i class="fa fa-bed"></i> Rooms
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my_bookings.php">
                            <i class="fa fa-calendar-check"></i> My Bookings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reviews.php">
                            <i class="fa fa-star"></i> Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="support.php">
                            <i class="fa fa-headset"></i> Support
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">
                            <i class="fa fa-envelope"></i> Contact Us
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <!-- Search Button -->
                    <div class="nav-search me-3">
                        <button class="btn btn-light btn-sm rounded-circle" type="button" data-bs-toggle="modal" data-bs-target="#searchModal">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                    <?php if ($is_logged_in): ?>
                    <!-- User Account -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="<?= $user_img ?>" alt="User" class="rounded-circle me-2" width="32" height="32" onerror="this.src='assets/images/user-placeholder.jpg'">
                            <span>Account</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="activity.php"><i class="fa fa-history me-2"></i> Activity Log</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fa fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="login.php" class="btn btn-outline-light ms-2"><i class="fa fa-sign-in-alt me-1"></i> Login</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchModalLabel">Search Hotels & Rooms</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="hotels.php" method="get">
                        <div class="mb-3">
                            <label for="searchQuery" class="form-label">Search Query</label>
                            <input type="text" class="form-control" id="searchQuery" name="search" placeholder="Hotel name, location, amenities...">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="searchCity" class="form-label">City</label>
                                <input type="text" class="form-control" id="searchCity" name="city" placeholder="Enter city">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="searchType" class="form-label">Hotel Type</label>
                                <select class="form-select" id="searchType" name="type">
                                    <option value="">All Types</option>
                                    <option value="Luxury">Luxury</option>
                                    <option value="Business">Business</option>
                                    <option value="Resort">Resort</option>
                                    <option value="Boutique">Boutique</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Navbar Scroll Effect -->
    <script>
        // Add scroll effect to navbar
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.modern-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>

    <!-- Main Content Container -->
    <div class="page-content">
