<?php
// User Room Booking Page
include 'includes/header.php';
include 'includes/db.php';
// Get all room types
$room_types_res = $mysqli->query("SELECT * FROM room_types ORDER BY type_name");
$room_types = [];
while($rt = $room_types_res->fetch_assoc()) {
    $room_types[$rt['room_type_id']] = $rt['type_name'];
}
// Get all amenities
$amenities_res = $mysqli->query("SELECT * FROM amenities ORDER BY amenity_name");
$all_amenities = [];
while($am = $amenities_res->fetch_assoc()) {
    $all_amenities[] = $am;
}
?>
<style>
.modern-booking-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.booking-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.booking-header {
    background: linear-gradient(135deg, #4e73df 0%, #00d4aa 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.booking-header h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.booking-body {
    padding: 2.5rem;
}

.form-group-modern {
    margin-bottom: 2rem;
}

.form-label-modern {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-label-modern i {
    color: #4e73df;
    font-size: 1rem;
}

.form-control-modern {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control-modern:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
    outline: none;
    transform: translateY(-2px);
}

.amenities-section {
    background: rgba(78, 115, 223, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.amenity-item {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.amenity-item:hover {
    border-color: #4e73df;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.15);
}

.amenity-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #4e73df;
}

.amenity-label {
    font-weight: 500;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.price-display {
    background: linear-gradient(135deg, #4e73df 0%, #00d4aa 100%);
    color: white;
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    margin: 2rem 0;
}

.price-display .price-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-display .price-amount {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
}

.booking-btn {
    background: linear-gradient(135deg, #4e73df 0%, #00d4aa 100%);
    color: white;
    border: none;
    border-radius: 16px;
    padding: 1.25rem 2rem;
    font-size: 1.1rem;
    font-weight: 700;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
}

.booking-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(78, 115, 223, 0.4);
}

.booking-btn:active {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .modern-booking-container {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }

    .booking-body {
        padding: 1.5rem;
    }

    .booking-header {
        padding: 1.5rem;
    }

    .booking-header h3 {
        font-size: 1.5rem;
    }
}
</style>

<div class="modern-booking-container">
    <div class="booking-card">
        <div class="booking-header">
            <h3><i class="fas fa-bed me-2"></i>Book Your Perfect Room</h3>
        </div>
        <div class="booking-body">
            <form method="post" action="process_booking.php" id="bookingForm">
                <div class="form-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-bed"></i>
                        Room Type
                    </label>
                    <select class="form-control-modern" name="room_type_id" id="roomTypeSelect" required>
                        <option value="">Select Room Type</option>
                        <?php foreach($room_types as $id => $name): ?>
                        <option value="<?= $id ?>"><?= htmlspecialchars($name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-door-open"></i>
                        Room Number
                    </label>
                    <select class="form-control-modern" name="room_id" id="roomNumberSelect" required disabled>
                        <option value="">Select Room Number</option>
                    </select>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-modern">
                            <label for="check_in" class="form-label-modern">
                                <i class="fas fa-calendar-check"></i>
                                Check-in Date
                            </label>
                            <input type="date" class="form-control-modern" name="check_in" id="check_in" required min="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-modern">
                            <label for="check_out" class="form-label-modern">
                                <i class="fas fa-calendar-times"></i>
                                Check-out Date
                            </label>
                            <input type="date" class="form-control-modern" name="check_out" id="check_out" required min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                        </div>
                    </div>
                </div>

                <div class="form-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-comment-alt"></i>
                        Special Requests
                    </label>
                    <textarea class="form-control-modern" name="special_requests" rows="3" placeholder="Let us know your preferences..."></textarea>
                </div>
                <div class="amenities-section">
                    <label class="form-label-modern">
                        <i class="fas fa-plus-circle"></i>
                        Select Additional Amenities
                    </label>
                    <?php if(count($all_amenities) > 0): ?>
                        <?php foreach($all_amenities as $am): ?>
                            <?php
                            // Set default prices for amenities since price column doesn't exist in amenities table
                            $amenity_prices = [
                                1 => 500,  // Spa Access
                                2 => 300,  // Lunch Meal
                                3 => 450,  // Dinner Meal
                                4 => 150,  // Snacks Package
                                5 => 800,  // In-Room Massage
                                6 => 200   // Default for any other amenity
                            ];
                            $price = $amenity_prices[$am['amenity_id']] ?? 200;

                            // Set icons for amenities
                            $amenity_icons = [
                                1 => 'spa',
                                2 => 'utensils',
                                3 => 'utensils',
                                4 => 'coffee',
                                5 => 'spa',
                                6 => 'star'
                            ];
                            $icon = $amenity_icons[$am['amenity_id']] ?? 'star';
                            ?>
                            <div class="amenity-item">
                                <input class="amenity-checkbox" type="checkbox" name="amenities[]" value="<?php echo $am['amenity_id']; ?>" data-price="<?php echo $price; ?>" id="amenity<?php echo $am['amenity_id']; ?>">
                                <div class="amenity-info">
                                    <i class="fas fa-<?php echo $icon; ?>" style="color: #4e73df; margin-right: 0.5rem;"></i>
                                </div>
                                <label class="amenity-label" for="amenity<?php echo $am['amenity_id']; ?>">
                                    <?php echo htmlspecialchars($am['amenity_name']); ?>
                                </label>
                                <div class="amenity-price" style="font-weight: 600; color: #00d4aa;">
                                    +₱<?php echo number_format($price, 2); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p style="text-align: center; color: #6c757d; margin: 1rem 0;">No additional amenities available</p>
                    <?php endif; ?>
                </div>

                <div class="price-display">
                    <div class="price-label">Total Price</div>
                    <div class="price-amount" id="totalPrice">₱0.00</div>
                </div>

                <button class="booking-btn" type="submit">
                    <i class="fas fa-credit-card me-2"></i>
                    Complete Payment
                </button>
            </form>
        </div>
    </div>
</div>
<script>
const roomTypeSelect = document.getElementById('roomTypeSelect');
const roomNumberSelect = document.getElementById('roomNumberSelect');
const totalPriceDiv = document.getElementById('totalPrice');
let basePrice = 0;
let roomData = {};

roomTypeSelect.addEventListener('change', function() {
    const typeId = this.value;
    if (!typeId) {
        roomNumberSelect.innerHTML = '<option value="">Select Room Number</option>';
        roomNumberSelect.disabled = true;
        basePrice = 0;
        updateTotal();
        return;
    }
    fetch('get_rooms_by_type.php?type_id=' + typeId)
        .then(res => res.json())
        .then(data => {
            roomData = data.rooms;
            let options = '<option value="">Select Room Number</option>';
            for (let i = 1; i <= 20; i++) {
                let num = i.toString().padStart(2, '0');
                let found = roomData.find(r => r.room_number == num);
                if (found) {
                    options += `<option value="${found.room_id}" data-price="${found.price}">Room ${num} (₱${parseFloat(found.price).toLocaleString(undefined,{minimumFractionDigits:2})})</option>`;
                } else {
                    options += `<option value="" disabled>Room ${num} (Not Created)</option>`;
                }
            }
            roomNumberSelect.innerHTML = options;
            roomNumberSelect.disabled = false;
            basePrice = 0;
            updateTotal();
        });
});
roomNumberSelect.addEventListener('change', function() {
    const selected = this.options[this.selectedIndex];
    basePrice = selected && selected.dataset.price ? parseFloat(selected.dataset.price) : 0;
    updateTotal();
});
const amenityCheckboxes = document.querySelectorAll('.amenity-checkbox');
function updateTotal() {
    let total = parseFloat(basePrice);
    amenityCheckboxes.forEach(cb => { if(cb.checked) total += parseFloat(cb.dataset.price); });
    totalPriceDiv.textContent = '₱' + total.toLocaleString(undefined, {minimumFractionDigits:2});
}
amenityCheckboxes.forEach(cb => cb.addEventListener('change', updateTotal));
</script>
<?php include 'includes/footer.php'; ?>
