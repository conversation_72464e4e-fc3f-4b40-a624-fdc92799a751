<?php
// User Room Booking Page
include 'includes/header.php';
include 'includes/db.php';
// Get all room types
$room_types_res = $mysqli->query("SELECT * FROM room_types ORDER BY type_name");
$room_types = [];
while($rt = $room_types_res->fetch_assoc()) {
    $room_types[$rt['room_type_id']] = $rt['type_name'];
}
// Get all amenities
$amenities_res = $mysqli->query("SELECT * FROM amenities ORDER BY name");
$all_amenities = [];
while($am = $amenities_res->fetch_assoc()) {
    $all_amenities[] = $am;
}
?>
<div class="container" style="margin-top:2rem;max-width:600px;">
    <div class="card shadow mb-4">
        <div class="card-body">
            <h3 class="mb-3" style="font-weight:600;">Book a Room</h3>
            <form method="post" action="process_booking.php" id="bookingForm">
                <div class="mb-3">
                    <label class="form-label">Room Type</label>
                    <select class="form-select" name="room_type_id" id="roomTypeSelect" required>
                        <option value="">Select Room Type</option>
                        <?php foreach($room_types as $id => $name): ?>
                        <option value="<?= $id ?>"><?= htmlspecialchars($name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Room Number</label>
                    <select class="form-select" name="room_id" id="roomNumberSelect" required disabled>
                        <option value="">Select Room Number</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="check_in" class="form-label">Check-in Date</label>
                    <input type="date" class="form-control" name="check_in" id="check_in" required min="<?php echo date('Y-m-d'); ?>">
                </div>
                <div class="mb-3">
                    <label for="check_out" class="form-label">Check-out Date</label>
                    <input type="date" class="form-control" name="check_out" id="check_out" required min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Special Requests</label>
                    <textarea class="form-control" name="special_requests" rows="2" placeholder="Let us know your preferences..."></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Select Amenities</label>
                    <?php if(count($all_amenities) > 0): ?>
                        <?php foreach($all_amenities as $am): ?>
                        <div class="form-check">
                            <input class="form-check-input amenity-checkbox" type="checkbox" name="amenities[]" value="<?php echo $am['amenity_id']; ?>" data-price="<?php echo $am['price']; ?>" id="amenity<?php echo $am['amenity_id']; ?>">
                            <label class="form-check-label" for="amenity<?php echo $am['amenity_id']; ?>">
                                <?php echo htmlspecialchars($am['name']); ?> (+₱<?php echo number_format($am['price'],2); ?>)
                            </label>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <div class="mb-3">
                    <label class="form-label">Total Price</label>
                    <div class="form-control-plaintext" id="totalPrice" style="font-weight:600;font-size:1.2rem;">₱0.00</div>
                </div>
                <button class="btn btn-success w-100" type="submit">Complete Payment</button>
            </form>
        </div>
    </div>
</div>
<script>
const roomTypeSelect = document.getElementById('roomTypeSelect');
const roomNumberSelect = document.getElementById('roomNumberSelect');
const totalPriceDiv = document.getElementById('totalPrice');
let basePrice = 0;
let roomData = {};

roomTypeSelect.addEventListener('change', function() {
    const typeId = this.value;
    if (!typeId) {
        roomNumberSelect.innerHTML = '<option value="">Select Room Number</option>';
        roomNumberSelect.disabled = true;
        basePrice = 0;
        updateTotal();
        return;
    }
    fetch('get_rooms_by_type.php?type_id=' + typeId)
        .then(res => res.json())
        .then(data => {
            roomData = data.rooms;
            let options = '<option value="">Select Room Number</option>';
            for (let i = 1; i <= 20; i++) {
                let num = i.toString().padStart(2, '0');
                let found = roomData.find(r => r.room_number == num);
                if (found) {
                    options += `<option value="${found.room_id}" data-price="${found.price}">Room ${num} (₱${parseFloat(found.price).toLocaleString(undefined,{minimumFractionDigits:2})})</option>`;
                } else {
                    options += `<option value="" disabled>Room ${num} (Not Created)</option>`;
                }
            }
            roomNumberSelect.innerHTML = options;
            roomNumberSelect.disabled = false;
            basePrice = 0;
            updateTotal();
        });
});
roomNumberSelect.addEventListener('change', function() {
    const selected = this.options[this.selectedIndex];
    basePrice = selected && selected.dataset.price ? parseFloat(selected.dataset.price) : 0;
    updateTotal();
});
const amenityCheckboxes = document.querySelectorAll('.amenity-checkbox');
function updateTotal() {
    let total = parseFloat(basePrice);
    amenityCheckboxes.forEach(cb => { if(cb.checked) total += parseFloat(cb.dataset.price); });
    totalPriceDiv.textContent = '₱' + total.toLocaleString(undefined, {minimumFractionDigits:2});
}
amenityCheckboxes.forEach(cb => cb.addEventListener('change', updateTotal));
</script>
<?php include 'includes/footer.php'; ?>
