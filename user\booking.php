<?php
// User Room Booking Page
include 'includes/header.php';
include 'includes/db.php';
$room_id = isset($_GET['room_id']) ? (int)$_GET['room_id'] : 0;
$res = $mysqli->query("SELECT r.*, h.hotel_name, h.address, h.city FROM rooms r JOIN hotels h ON r.hotel_id=h.hotel_id WHERE r.room_id=$room_id LIMIT 1");
$room = $res->fetch_assoc();
if (!$room) { echo '<div class="container mt-5"><div class="alert alert-danger">Room not found.</div></div>'; include 'includes/footer.php'; exit; }
// Fetch amenities for this room type
$type_id = $room['room_type_id'];
$amenities = $mysqli->query("SELECT a.*, ra.room_id FROM amenities a JOIN room_amenities ra ON a.amenity_id=ra.amenity_id WHERE ra.room_type_id=$type_id");
?>
<div class="container" style="margin-top:2rem;max-width:600px;">
    <div class="card shadow mb-4">
        <div class="card-body">
            <h3 class="mb-3" style="font-weight:600;">Book Room <?php echo htmlspecialchars($room['room_number']); ?> at <?php echo htmlspecialchars($room['hotel_name']); ?></h3>
            <form method="post" action="process_booking.php">
                <input type="hidden" name="room_id" value="<?php echo $room['room_id']; ?>">
                <div class="mb-3">
                    <label for="check_in" class="form-label">Check-in Date</label>
                    <input type="date" class="form-control" name="check_in" id="check_in" required min="<?php echo date('Y-m-d'); ?>">
                </div>
                <div class="mb-3">
                    <label for="check_out" class="form-label">Check-out Date</label>
                    <input type="date" class="form-control" name="check_out" id="check_out" required min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Special Requests</label>
                    <textarea class="form-control" name="special_requests" rows="2" placeholder="Let us know your preferences..."></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Select Amenities</label>
                    <?php if($amenities->num_rows > 0): ?>
                    <?php while($am = $amenities->fetch_assoc()): ?>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="amenities[]" value="<?php echo $am['amenity_id']; ?>" data-price="<?php echo $am['price']; ?>" id="amenity<?php echo $am['amenity_id']; ?>">
                        <label class="form-check-label" for="amenity<?php echo $am['amenity_id']; ?>">
                            <?php echo htmlspecialchars($am['name']); ?> (+₱<?php echo number_format($am['price'],2); ?>)
                        </label>
                    </div>
                    <?php endwhile; ?>
                    <?php else: ?>
                    <div class="text-muted">No extra amenities available for this room type.</div>
                    <?php endif; ?>
                </div>
                <div class="mb-3">
                    <label class="form-label">Total Price</label>
                    <div class="form-control-plaintext" id="totalPrice" style="font-weight:600;font-size:1.2rem;">₱<?php echo number_format($room['price'],2); ?></div>
                </div>
                <button class="btn btn-success w-100" type="submit">Complete Payment</button>
            </form>
        </div>
    </div>
</div>
<script>
// JS to update total price with amenities
const amenityCheckboxes = document.querySelectorAll('input[name="amenities[]"]');
const totalPriceDiv = document.getElementById('totalPrice');
let basePrice = <?php echo json_encode($room['price']); ?>;
function updateTotal() {
    let total = parseFloat(basePrice);
    amenityCheckboxes.forEach(cb => { if(cb.checked) total += parseFloat(cb.dataset.price); });
    totalPriceDiv.textContent = '₱' + total.toLocaleString(undefined, {minimumFractionDigits:2});
}
amenityCheckboxes.forEach(cb => cb.addEventListener('change', updateTotal));
</script>
<?php include 'includes/footer.php'; ?>
