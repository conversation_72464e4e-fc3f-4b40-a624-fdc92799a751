<?php
session_start();
// User Profile Page
include 'includes/header.php';
include 'includes/db.php';
$user_id = $_SESSION['user_id'] ?? 0;
if (!$user_id) { header('Location: login.php?msg=Please+login+first'); exit; }
$res = $mysqli->query("SELECT * FROM users WHERE user_id=$user_id LIMIT 1");
$user = $res->fetch_assoc();
if (!$user) { echo '<div class="container mt-5"><div class="alert alert-danger">User not found.</div></div>'; include 'includes/footer.php'; exit; }
?>
<div class="container" style="margin-top:2rem;max-width:600px;">
    <div class="card shadow-lg border-0 rounded-4 animate__animated animate__fadeInDown">
        <div class="card-body p-5">
            <div class="text-center mb-4">
                <img src="assets/images/profile-<?=intval($user['user_id'])?>.jpg" onerror="this.src='assets/images/admin-profile-1.jpg'" class="rounded-circle shadow" style="width:120px;height:120px;object-fit:cover;border:4px solid #4e73df;">
                <h2 class="mt-3 mb-1" style="font-weight:700;letter-spacing:0.5px;">My Profile</h2>
                <span class="badge bg-primary bg-gradient mb-2" style="font-size:1rem;">User #<?=intval($user['user_id'])?></span>
            </div>
            <form method="post" action="update_profile.php" enctype="multipart/form-data" autocomplete="off">
                <div class="mb-3">
                    <label class="form-label">First Name</label>
                    <input type="text" class="form-control form-control-lg rounded-3" name="first_name" value="<?=htmlspecialchars($user['first_name'])?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Last Name</label>
                    <input type="text" class="form-control form-control-lg rounded-3" name="last_name" value="<?=htmlspecialchars($user['last_name'])?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control form-control-lg rounded-3" name="email" value="<?=htmlspecialchars($user['email'])?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Phone</label>
                    <input type="text" class="form-control form-control-lg rounded-3" name="phone" value="<?=htmlspecialchars($user['phone'])?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Profile Image</label>
                    <input type="file" class="form-control form-control-lg rounded-3" name="profile_image" accept="image/*">
                </div>
                <button class="btn btn-primary btn-lg w-100 mt-2" type="submit">Update Profile</button>
            </form>
        </div>
    </div>
</div>
<style>
.card.animate__fadeInDown {animation-duration: 0.7s;}
</style>
<?php include 'includes/footer.php'; ?>
