<?php
// Modern Bookings Management
// Functionality: List, edit, delete bookings
// Design: Modern, responsive, visually consistent
// Code Neatness: Well-structured, meaningful comments
include 'includes/db.php';
include 'includes/header.php';
include '../user/includes/amenity_pricing.php';
?>
<div class="admin-content">
    <h2>Bookings</h2>
    <div style="display:flex;gap:1rem;margin-bottom:1rem;">
        <a href="export_bookings.php" class="btn btn-success" target="_blank"><i class="fa fa-file-excel"></i> Export to Excel</a>
        <button class="btn btn-primary" onclick="window.print()"><i class="fa fa-print"></i> Print</button>
    </div>
    <table class="table" id="bookingsTable">
        <tr>
            <th>ID</th><th>User</th><th>Room</th><th>Check-in</th><th>Check-out</th><th>Status</th><th>Payment</th><th>Actions</th>
        </tr>
        <?php
        $res = $mysqli->query("SELECT b.*, u.first_name, u.last_name, r.room_number FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id");
        while ($row = $res->fetch_assoc()) {
            // Fetch amenities for this booking
            $amenities = [];
            $a_res = $mysqli->query("SELECT ba.amenity_id, a.amenity_name as name, ba.custom_price FROM booking_amenities ba JOIN amenities a ON ba.amenity_id = a.amenity_id WHERE ba.booking_id = " . (int)$row['booking_id']);
            while ($a = $a_res->fetch_assoc()) {
                $price = $a['custom_price'] !== null ? $a['custom_price'] : getAmenityPrice($a['amenity_id']);
                $amenities[] = htmlspecialchars($a['name']) . ' (+₱' . number_format($price, 2) . ')';
            }
            $amenities_str = $amenities ? implode('<br>', $amenities) : '<span class="text-muted">None</span>';
            echo '<tr>
                <td>'.$row['booking_id'].'</td>
                <td>'.htmlspecialchars($row['first_name'].' '.$row['last_name']).'</td>
                <td>'.htmlspecialchars($row['room_number']).'<br><small>'.$amenities_str.'</small></td>
                <td>'.$row['check_in_date'].'</td>
                <td>'.$row['check_out_date'].'</td>
                <td>'.htmlspecialchars($row['booking_status']).'</td>
                <td>'.htmlspecialchars($row['payment_status']).'</td>
                <td>
                    <a class="btn" href="edit_booking.php?id='.$row['booking_id'].'">Edit</a>
                    <a class="btn btn-delete" href="delete_booking.php?id='.$row['booking_id'].'" onclick="return confirm(\'Delete booking?\')">Delete</a>
                </td>
            </tr>';
        }
        ?>
    </table>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.min.js"></script>
<script>
// Real-time update for bookings (requires backend socket.io server)
const socket = io('http://localhost:3000');
socket.on('bookings_update', function() {
    location.reload();
});
</script>
<?php include 'includes/footer.php'; ?>
