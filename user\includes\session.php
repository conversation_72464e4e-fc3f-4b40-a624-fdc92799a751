<?php
/**
 * Session Management File
 * 
 * This file should be included at the very top of all pages that need session access
 * before any HTML output or includes that produce output.
 */

// Start the session if it hasn't been started already
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 * 
 * @return bool True if user is logged in, false otherwise
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Get current user ID
 * 
 * @return int User ID or 0 if not logged in
 */
function get_user_id() {
    return $_SESSION['user_id'] ?? 0;
}

/**
 * Require user to be logged in, redirect if not
 * 
 * @param string $redirect_url URL to redirect to if not logged in
 * @return void
 */
function require_login($redirect_url = 'login.php?msg=Please+login+first') {
    if (!is_logged_in()) {
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Set a flash message to be displayed on the next page
 * 
 * @param string $message Message to display
 * @param string $type Message type (success, danger, warning, info)
 * @return void
 */
function set_flash_message($message, $type = 'info') {
    $_SESSION['flash_message'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Get and clear flash message
 * 
 * @return array|null Flash message array or null if no message
 */
function get_flash_message() {
    $message = $_SESSION['flash_message'] ?? null;
    unset($_SESSION['flash_message']);
    return $message;
}
?>
