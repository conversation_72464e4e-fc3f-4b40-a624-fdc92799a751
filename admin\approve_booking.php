<?php
// approve_booking.php - Mark booking as paid/approved and notify guest via <PERSON><PERSON><PERSON>ailer
use <PERSON>HPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';

$booking_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if ($booking_id) {
    // Update booking status and payment
    $mysqli->query("UPDATE bookings SET booking_status='confirmed', payment_status='paid' WHERE booking_id=$booking_id");
    // Get guest email and details
    $res = $mysqli->query("SELECT u.email, u.first_name, u.last_name, h.hotel_name, r.room_number, b.check_in_date, b.check_out_date FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.booking_id=$booking_id LIMIT 1");
    if ($row = $res->fetch_assoc()) {
        // Get email settings from database
        $settings = [
            'smtp_host' => 'smtp.example.com',
            'smtp_port' => '587',
            'smtp_secure' => 'tls',
            'smtp_auth' => '1',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'yourpassword',
            'from_email' => '<EMAIL>',
            'from_name' => 'Hotel Admin',
        ];

        // Load settings from database if they exist
        $result = $mysqli->query("SELECT * FROM system_settings LIMIT 1");
        if ($result && $result->num_rows > 0) {
            $settings_row = $result->fetch_assoc();
            // Update settings with database values if they exist
            if (!empty($settings_row['smtp_host'])) $settings['smtp_host'] = $settings_row['smtp_host'];
            if (!empty($settings_row['smtp_port'])) $settings['smtp_port'] = $settings_row['smtp_port'];
            if (!empty($settings_row['smtp_username'])) $settings['smtp_username'] = $settings_row['smtp_username'];
            if (!empty($settings_row['smtp_password'])) $settings['smtp_password'] = $settings_row['smtp_password'];
            if (!empty($settings_row['smtp_secure'])) $settings['smtp_secure'] = $settings_row['smtp_secure'];
            if (!empty($settings_row['from_email'])) $settings['from_email'] = $settings_row['from_email'];
            if (!empty($settings_row['from_name'])) $settings['from_name'] = $settings_row['from_name'];
        }

        $mail = new PHPMailer(true);
        try {
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->Port = $settings['smtp_port'];

            if ($settings['smtp_secure'] === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            } elseif ($settings['smtp_secure'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } else {
                // For 'none', set to empty string instead of false
                $mail->SMTPSecure = '';
                $mail->SMTPAutoTLS = false;
            }

            if ($settings['smtp_auth'] === '1') {
                $mail->SMTPAuth = true;
                $mail->Username = $settings['smtp_username'];
                $mail->Password = $settings['smtp_password'];
            } else {
                $mail->SMTPAuth = false;
            }

            $mail->setFrom($settings['from_email'], $settings['from_name']);
            $mail->addAddress($row['email'], $row['first_name'].' '.$row['last_name']);
            $mail->isHTML(true);
            $mail->Subject = 'Your Booking is Confirmed and Paid';
            $mail->Body = '<b>Dear '.htmlspecialchars($row['first_name']).',</b><br><br>Your booking at <b>'.htmlspecialchars($row['hotel_name']).'</b> (Room '.$row['room_number'].') from <b>'.$row['check_in_date'].'</b> to <b>'.$row['check_out_date'].'</b> has been <b>confirmed and marked as paid</b>.<br><br>Thank you for booking with us!';
            $mail->send();
            header('Location: dashboard.php?msg=Booking+approved+and+guest+notified');
            exit;
        } catch (Exception $e) {
            header('Location: dashboard.php?msg=Booking+approved+but+email+failed');
            exit;
        }
    }
}
header('Location: dashboard.php?msg=Invalid+booking');
exit;
