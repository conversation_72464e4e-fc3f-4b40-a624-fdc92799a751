<?php
// approve_booking.php - Mark booking as paid/approved and notify guest via <PERSON><PERSON><PERSON>ailer
use <PERSON>HPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/PHPMailer.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/Exception.php';
require_once __DIR__ . '/../PHPMailer-PHPMailer-19debc7/src/SMTP.php';
include 'includes/db.php';

$booking_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if ($booking_id) {
    // Update booking status and payment
    $mysqli->query("UPDATE bookings SET booking_status='confirmed', payment_status='paid' WHERE booking_id=$booking_id");
    // Get guest email and details
    $res = $mysqli->query("SELECT u.email, u.first_name, u.last_name, h.hotel_name, r.room_number, b.check_in_date, b.check_out_date FROM bookings b JOIN users u ON b.user_id=u.user_id JOIN rooms r ON b.room_id=r.room_id JOIN hotels h ON r.hotel_id=h.hotel_id WHERE b.booking_id=$booking_id LIMIT 1");
    if ($row = $res->fetch_assoc()) {
        // Get email settings from database
        $settings = [
            'smtp_host' => 'smtp.example.com',
            'smtp_port' => '587',
            'smtp_secure' => 'tls',
            'smtp_auth' => '1',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'yourpassword',
            'from_email' => '<EMAIL>',
            'from_name' => 'Hotel Admin',
        ];

        // Load settings from database if they exist
        $result = $mysqli->query("SELECT * FROM system_settings LIMIT 1");
        if ($result && $result->num_rows > 0) {
            $settings_row = $result->fetch_assoc();
            // Update settings with database values if they exist
            if (!empty($settings_row['smtp_host'])) $settings['smtp_host'] = $settings_row['smtp_host'];
            if (!empty($settings_row['smtp_port'])) $settings['smtp_port'] = $settings_row['smtp_port'];
            if (!empty($settings_row['smtp_username'])) $settings['smtp_username'] = $settings_row['smtp_username'];
            if (!empty($settings_row['smtp_password'])) $settings['smtp_password'] = $settings_row['smtp_password'];
            if (!empty($settings_row['smtp_secure'])) $settings['smtp_secure'] = $settings_row['smtp_secure'];
            if (!empty($settings_row['from_email'])) $settings['from_email'] = $settings_row['from_email'];
            if (!empty($settings_row['from_name'])) $settings['from_name'] = $settings_row['from_name'];
        }

        // Check if email settings are properly configured
        if (empty($settings['smtp_host']) || $settings['smtp_host'] === 'smtp.example.com' ||
            empty($settings['from_email']) || $settings['from_email'] === '<EMAIL>') {
            // Email not configured, skip sending but still approve booking
            header('Location: dashboard.php?msg=Booking+approved+but+email+not+configured');
            exit;
        }

        $mail = new PHPMailer(true);
        try {
            // Enable verbose debug output for troubleshooting (comment out in production)
            $mail->SMTPDebug = 2;
            $mail->Debugoutput = function($str, $level) {
                error_log("SMTP Debug: " . $str);
            };

            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->Port = intval($settings['smtp_port']);

            if ($settings['smtp_secure'] === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            } elseif ($settings['smtp_secure'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } else {
                // For 'none', set to empty string instead of false
                $mail->SMTPSecure = '';
                $mail->SMTPAutoTLS = false;
            }

            if ($settings['smtp_auth'] === '1' || $settings['smtp_auth'] === 1) {
                $mail->SMTPAuth = true;
                $mail->Username = $settings['smtp_username'];
                $mail->Password = $settings['smtp_password'];
            } else {
                $mail->SMTPAuth = false;
            }

            // Set timeout values and additional options for Gmail
            $mail->Timeout = 60;
            $mail->SMTPKeepAlive = false;
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );

            $mail->setFrom($settings['from_email'], $settings['from_name']);
            $mail->addAddress($row['email'], $row['first_name'].' '.$row['last_name']);
            $mail->isHTML(true);
            $mail->Subject = 'Your Booking is Confirmed and Paid';
            $mail->Body = '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e3e6f0; border-radius: 5px;">
                    <h2 style="color: #4e73df; text-align: center;">Booking Confirmed</h2>
                    <p><b>Dear '.htmlspecialchars($row['first_name']).',</b></p>
                    <p>Great news! Your booking has been confirmed and marked as paid.</p>
                    <div style="background-color: #f8f9fc; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>Hotel:</strong> '.htmlspecialchars($row['hotel_name']).'</p>
                        <p style="margin: 5px 0;"><strong>Room:</strong> '.$row['room_number'].'</p>
                        <p style="margin: 5px 0;"><strong>Check-in:</strong> '.$row['check_in_date'].'</p>
                        <p style="margin: 5px 0;"><strong>Check-out:</strong> '.$row['check_out_date'].'</p>
                    </div>
                    <p>Thank you for booking with us! We look forward to welcoming you.</p>
                    <p style="text-align: center; margin-top: 20px; color: #858796; font-size: 14px;">
                        This is an automated message. Please do not reply to this email.
                    </p>
                </div>
            ';

            // Create plain text version
            $mail->AltBody = "Dear {$row['first_name']},\n\nYour booking has been confirmed and marked as paid.\n\nHotel: {$row['hotel_name']}\nRoom: {$row['room_number']}\nCheck-in: {$row['check_in_date']}\nCheck-out: {$row['check_out_date']}\n\nThank you for booking with us!";

            $mail->send();
            header('Location: dashboard.php?msg=Booking+approved+and+guest+notified');
            exit;
        } catch (Exception $e) {
            // Log the error for debugging
            error_log("Email sending failed: " . $mail->ErrorInfo);

            // For debugging, you can temporarily show the error (remove in production)
            $error_msg = urlencode("Email failed: " . $mail->ErrorInfo);
            header("Location: dashboard.php?msg=Booking+approved+but+email+failed&error={$error_msg}");
            exit;
        }
    }
}
header('Location: dashboard.php?msg=Invalid+booking');
exit;
